{% extends "hisensehr/layout/user_base.html" %}
{% block body_block %}
{% load static %} 
{% load my_filters %}
<style>
  .status-label {
    margin-right: auto; /* Push the element to the left */
    line-height: 1.5;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 14px;
  }
  input[disabled], select[disabled] {
      cursor: not-allowed !important;
      background-color: #e9ecef !important; 
      color: #6c757d !important;            
  }
  select#measurement_type[disabled] {
      cursor: not-allowed !important;
      color: #6c757d !important;            
  }
  .inactive-row input,
  .inactive-row select {
      background: #e9ecef !important;
      color: #6c757d !important;
      pointer-events: none;
      border-color: #ced4da !important;
  }
  .inactive-row {
      background: #f8f9fa !important;
  }
  .inactive-row .manage_kpi_goal_delete {
      pointer-events: auto !important;
      opacity: 1 !important;
      color: #dc3545 !important; /* or your action color */
  }
  .scrollable-table-wrapper {
    max-height: 70vh; /* Adjust height as needed */
    overflow-y: auto;
  }
</style>
<input type="hidden" name="dept-name" id="user_dept" value="{{selected_user.profile.department.id}}">
<input type="hidden" name="user-id" id="user_id" value="{{selected_user.id}}">
<input type="hidden" name="period-id" id="period_id" value="{{period}}">
<input type="hidden" value="{% url 'appdashboard:kpi_goal_action' userid action period %}" id="kpi_goal_action_url">
<div class="content">
    <input type="hidden" id="current_year">
    <div class="main-contents full-screen">
        <div class="page-top">
            <div>
            <h4>KPI</h4>
            <p><a href="{% url 'appdashboard:dashboard' %}">Dashboard</a><span>/<span><a href="">KPI Management</a><span>/<span><a href="" class="current-page">Assign User Goals</a><span></p>
            <!-- <p><a href="{% url 'appdashboard:dashboard' %}">Dashboard</a><span>/<span><a href="" class="current-page">KPI</a><span></p> -->
        </div>
        </div> 
        <div class="main-content-inner">
            
            <div class="table-card-block kpi-asign-goal-values table-full-screen table-kpi assign-kpi mt-20 ">
                <div class="table-top d-flex justify-content-between align-items-center p-20">
                  <h4>KPI - Overview - {{selected_user.first_name}} {{selected_user.last_name}}</h4>
                  <span id="goal_status" class="status-label">
                    {% if has_user_goals %}
                      <span id="goal_status" class="status-label">
                          {% if has_inactive_goals %}
                              <span class="table-bg-red" id="inactive-text">Goals are inactive. Click 'Activate' button for approval.</span>
                          {% else %}
                            <span class="table-bg-green" id="active-text">Goals are active.</span>
                          {% endif %}
                      </span>
                  {% endif %}
                  </span>
                  <span  style="display: flex; align-items: center; gap: 10px;margin-right: 5%;">
                    <h4>KPI Year - {{current_year}}</h4>
                  </span>
                  <li class="filter-item" style="display: flex; align-items: center; gap: 10px;">
                    <div class="top-action">
                        <a href="{% url 'appdashboard:goal_settings'%}"  data-toggle="tooltip" title="KPI Goal Settings">
                          <img src="{% static 'admin/assets/images/back-button.svg' %}">
                        </a>   
                      </div> 
                 </li>
                </div>
            <div class="table_wrapper full_table_wrapper" id="goal_table">
                {% if not probation_completed  %}
                  <div class="table_wrapper hod_table_wrapper" id="kpi_table">
                    <div class="text-center p-5">
                        <h6 class="error_input">{{selected_user.first_name}} {{selected_user.last_name}} is in probation period. Joining date is {{selected_user.profile.date_of_joining|date:'d M y'}}.</h6>
                    </div>
                  </div>
                {% else %}
                <!-- <input type="text" id="new_row_data"> -->
                 <div class="scrollable-table-wrapper">
                  <table class="table table-borderless table-space-between table-target">
                    <thead>
                      <tr class="fw-m table_head">
                            <td scope="col" style="min-width:300px; width: 300px;">KPI</td>
                            <td scope="col" style="min-width:300px; width: 300px;">Goal</td>
                            <td scope="col" style="min-width:200px;">Measurement</td>
                            <td scope="col" style="min-width:200px;">Goal Value</td>
                            <td scope="col" style="min-width:200px;">Max Finish Rate (%)</td>
                            <td scope="col" style="min-width:200px;">Weight</td>
                            <td style="width:40px;"></td>
                      </tr>
                    </thead>
                    <tbody class="tbody-white" id="assign_goal_data" >
                      
                      {% include "hisensehr/kpi/goalset/assign_kpi_goal_values_ajax.html" %}

                      </tbody>
                  </table>
                 </div>
                {% endif %}
            </div>
            </div>

            <div class="total-weight">
              <div class="green-bottom custom-bottom">
                  <div colspan="5" style="text-align:right; border-right: 1px solid;  padding-right: 10px;">
                      Total weight
                  </div>
                  <div id="total_weight" style="margin-right: 10%; padding-left: 10px;">{{ total_weight }}</div>
              </div>
              <div class="d-flex button-block  justify-content-end">
                
                <button type="submit" id="assign_kpi"  data-url="{% url 'appdashboard:assign_kpi_goals' %}" class="gradient-button"><span class="btn-helper"></span>Activate</button>
              </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_script %}
<script src="{% static 'admin/assets/js/assign-kpi-goal-value.js' %}?v={% now 'd_m_yH:i:s'%}"></script>
<script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>


{% endblock page_script %}
from datetime import datetime
import os, sys, django
from urllib import response
from django.contrib.postgres.search import SearchVector

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "hisense_hrportal.settings")

from django.conf import settings
sys.path.append( settings.BASE_DIR )
django.setup()

# insert new records from excel sheet
import re
import os
import openpyxl
from django.db import transaction
from django.db.models import Sum, Q
from hisensehr.models import (
    User, KpiCategory, KpiGoal, MeasurementType, KpiYear,
    KpiTimePeriod, KpiUserYear, KpiUserGoal, KpiUserTimePeriod,KPILevels,
    KPIUserGoalValues, KPIApprovalStatus, KPILevelPermissions, Department, User_menu_access,KpiActionPermission,KPIExportLog,UserMobile,Dynamic_menus,KpiUserActual,KpiUserTarget,Profile,KpiAction
)
from hisensehr.constantvariables import GOAL_APPROVE_MENU, KPI_APPROVE_MENU, PERFORMANCE_CONFIG_MENU
from django.utils.text import slugify
import glob
import sys
import traceback


delete_log_path = os.path.join('media/kpi-logs', 'removed_kpi_log.txt')
def delete_log(msg):
    log_dir = os.path.dirname(delete_log_path)
    os.makedirs(log_dir, exist_ok=True)
    ts = datetime.now().isoformat(sep=' ', timespec='seconds')
    with open(delete_log_path, 'a', encoding='utf-8') as f:
        f.write(f"[{ts}] {msg}\n")


# # --- DELETE FUNCTION ---

def delete_kpi_user_year_and_related(user_year_id, remark=None):
    try:
        with transaction.atomic():
            user_year = KpiUserYear.objects.get(id=user_year_id)
            user = user_year.user

            delete_log(f"Starting deletion for user_year_id={user_year_id} (user={user.first_name} {user.last_name})")
            goal_ids = KpiUserGoal.objects.filter(kpi_user_year=user_year).values_list('id', flat=True)
            tp_ids = KpiUserTimePeriod.objects.filter(kpi_user_year=user_year).values_list('id', flat=True)
            KPIUserGoalValues.objects.filter(kpiuser_goal_id__in=goal_ids).delete()
            delete_log(" → KPIUserGoalValues deleted")

            KpiUserTimePeriod.objects.filter(kpi_user_year=user_year).delete()
            delete_log(" → KpiUserTimePeriod deleted")

            KpiUserGoal.objects.filter(kpi_user_year=user_year).delete()
            delete_log(" → KpiUserGoal deleted")

            KPIApprovalStatus.objects.filter(kpi_user_year=user_year).delete()
            delete_log(" → KPIApprovalStatus by user_year deleted")

            KPIApprovalStatus.objects.filter(kpi_user_year=user_year).delete()
            KPIApprovalStatus.objects.filter(kpiuser_timeperiod_id__in=tp_ids).delete()
            delete_log(" → KPIApprovalStatus by timeperiod and year deleted")

            KPILevelPermissions.objects.all().delete()
            delete_log(" → All KPILevelPermissions deleted")

            User_menu_access.objects.filter(
                user_id=user.id,
                dynamic_menu_id__in=[PERFORMANCE_CONFIG_MENU, KPI_APPROVE_MENU, GOAL_APPROVE_MENU]
            ).delete()
            delete_log(" → User_menu_access entries removed")

            KPIExportLog.objects.all().delete()
            delete_log(" → KPIExportLog entries removed")

            user_year.delete()
            delete_log(f" → KpiUserYear(id={user_year_id}) deleted")

            delete_log(f"Completed deletion for user_year_id={user_year_id}")
    except Exception as e:
        delete_log(f"Error deleting user_year_id={user_year_id}: {e}")

def parse_percentage_value(value):
    """
    Parse a value that might be a percentage string (like '100%'), a decimal, or a string with special characters.
    Returns the numeric value as a float in the 0-100 range.
    """
    if value is None or value == '':
        return 0.0

    if isinstance(value, str):
        value = value.replace('\xa0', '').replace(',', '').replace(' ', '').strip()  # Remove non-breaking spaces, commas, and spaces
        value_no_percent = value.replace('%', '').strip()
        # Try to extract the first number (int or float) from the string
        match = re.search(r'[-+]?[0-9]*\.?[0-9]+', value_no_percent)
        if match:
            try:
                return float(match.group())
            except Exception:
                return 0.0
        else:
            return 0.0

    if isinstance(value, (int, float)):
        # For numeric values, we need to determine if they represent percentages
        # Values like 1.19 could be 119% stored as decimal
        # Values like 0.98 could be 98% stored as decimal
        # But values like 100, 50, 25 are likely already in percentage format

        # If the value is between 0 and 10 (exclusive), it's likely a decimal percentage
        # Examples: 0.98 -> 98%, 1.19 -> 119%, 2.5 -> 250%
        if 0 < value < 10:
            return value * 100
        else:
            # Values >= 10 are likely already in percentage format
            return float(value)
    return 0.0

def calculate_achievement(actual, target, formula, max_finish_rate, weight):
    """
    Calculate and cap achievement percentage, then weight it.
    """
    # Convert inputs to numeric values
    try:
        actual = float(actual) if actual is not None else 0
    except (ValueError, TypeError):
        actual = 0
        
    try:
        target = float(target) if target is not None else 0
    except (ValueError, TypeError):
        target = 0
    
    if actual == 0 and target == 0:
        achieved = 100
    elif actual is not None and target is not None and target != 0:
        if formula:
            try:
                local_vars = {'actual': actual, 'target': target}
                safe_formula = formula.replace('actual', 'actual').replace('target', 'target')
                achieved = eval(safe_formula, {"__builtins__": {}}, local_vars)
            except Exception as e:
                print(f"Error evaluating formula '{formula}': {e}")
                print(f"Using actual={actual}, target={target}")
                achieved = (actual / target) * 100
        else:
            achieved = (actual / target) * 100
    else:
        return 0

    if achieved >= max_finish_rate:
        achieved = max_finish_rate

    return (achieved * weight) / 100

def import_kpi_goals_from_excel(filepath, year_name='2024'):
    # filepath = "media/KPI's/H2- KPIs - HR & Admins (1).xlsx"
    print("Processing file:", filepath)
    wb = openpyxl.load_workbook(filepath, data_only=True)
    kpi_level = KPILevels.objects.filter(action_key='kpi',level_key='level_1').last()

    def normalize_formula(formula):
        """
        Normalize formula strings to a consistent format for calculation.
        Handles various input formats from Excel and converts them to Python-executable formulas.
        """
        if not formula:
            return ''
            
        # Convert to lowercase and remove spaces and equals sign
        f = formula.lower().strip()
        if f.startswith('='):
            f = f[1:].strip()
        
        # Handle specific formula patterns
        if f == 'actual/target' or f == 'actual / target':
            return '(actual/target)*100'
        elif '2-actual/target' in f.replace(' ', '') or '2 - actual / target' in f:
            return '(2-(actual/target))*100'
        elif '3-actual/target' in f.replace(' ', '') or '3 - actual / target' in f:
            return '(3-(actual/target))*100'
        
        # If formula doesn't match any pattern, try to make it executable
        # Replace common patterns
        f = f.replace('actual/target', '(actual/target)*100')
        f = f.replace('actual / target', '(actual/target)*100')
        
        # Make sure we don't double-multiply by 100
        if '*100*100' in f:
            f = f.replace('*100*100', '*100')
        
        # Ensure formula has 'actual' and 'target' variables
        if 'actual' not in f or 'target' not in f:
            # Default to standard formula if variables are missing
            return '(actual/target)*100'
            
        return f

    def get_period_mappings(ws):
        """Extract period mappings from merged cells in row 1"""
        period_mappings = {}
        
        # Get merged cell ranges
        merged_ranges = ws.merged_cells.ranges
        for merged_range in merged_ranges:
            if merged_range.min_row == 1 and merged_range.max_row == 1:
                # This is a merged cell in row 1
                start_col = merged_range.min_col
                end_col = merged_range.max_col
                
                # Get the value from the merged cell
                cell_value = ws.cell(row=1, column=start_col).value
                if cell_value and isinstance(cell_value, str):
                    # Extract period name from patterns like "KPI Achievements - H1 2024"
                    if "KPI Achievements" in cell_value:
                        if "H1" in cell_value:
                            period_name = "H1"
                        elif "H2" in cell_value:
                            period_name = "H2"
                        else:
                            # Try to extract any period identifier
                            import re
                            match = re.search(r'[HQ]\d+', cell_value)
                            period_name = match.group() if match else None
                        
                        if period_name:
                            # Map all columns in this range to this period
                            for col in range(start_col, end_col + 1):
                                period_mappings[col] = period_name
        
        return period_mappings
    
    def fix_duplicate_headers(header, period_mappings):
        """Fix duplicate headers using period mappings"""
        fixed_header = []
        column_counts = {}
        
        for idx, col_name in enumerate(header):
            col_idx = idx + 1  # Excel columns are 1-indexed
            
            # Count occurrences of this column name
            if col_name not in column_counts:
                column_counts[col_name] = 0
            column_counts[col_name] += 1
            
            # If this is a duplicate and we have period mapping
            if column_counts[col_name] > 1 and col_idx in period_mappings:
                period = period_mappings[col_idx]
                
                # Fix common duplicate patterns
                if col_name == 'Target - H1':
                    fixed_name = f'Target - {period}'
                elif col_name == 'Actual - H1':
                    fixed_name = f'Actual - {period}'
                elif col_name == 'Achievement':
                    fixed_name = f'{period} Achievement'
                else:
                    # Generic fix for other duplicates
                    fixed_name = f'{col_name} - {period}'
                
                fixed_header.append(fixed_name)
            else:
                fixed_header.append(col_name)
        
        return fixed_header
    
    kpi_year = KpiYear.objects.filter(name__icontains=year_name).first()
    if not kpi_year:
        kpi_year = KpiYear.objects.create(
            name=f'KPI {year_name}',
            start_date=f'{year_name}-01-01',
            end_date=f'{year_name}-12-31',
            is_active=True,
            time_period=2,
            current_year=False
        )
        print(f"Created KpiYear: {kpi_year.name}")
    for ws in wb.worksheets:
        emp_code = ws['B1'].value
        sheet_name = ws.title
        print("Processing sheet:", sheet_name)
        raw_filename = os.path.basename(filepath)
        clean_filename = raw_filename.replace('\xa0', ' ').strip()
        filename_without_ext = os.path.splitext(clean_filename)[0]
        safe_filename = ''.join(c if c.isalnum() or c in [' ', '-', '_'] else '_' for c in filename_without_ext)
        safe_filename = safe_filename.strip()[:20]  # Limit to 20 chars
        # department_name = str(ws['B3'].value).strip() if ws['B3'].value else 'unknown_department'
        # safe_department_name = department_name.replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
        log_filename = f"kpi_import_log_{safe_filename}.txt"
        log_path = os.path.join('media/kpi-logs', log_filename)

        def insert_log(msg):
            log_dir = os.path.dirname(log_path)
            os.makedirs(log_dir, exist_ok=True)
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(f"{datetime.now()} - {msg}\n")

        def create_kpi_level_permissions(user, assigned_user, action_type, kpilevel):
            kpi_permission, created = KPILevelPermissions.objects.get_or_create(
                user_id=user,
                assigned_to=assigned_user,
                action_type=action_type,
                kpilevel_id=kpilevel,
                defaults={'is_active': True}
            )
            if created:
                insert_log(f"Created KPILevelPermissions for {user} - Action: {action_type} - Level: {kpilevel}")
            else:
                insert_log(f"KPILevelPermissions already exists for {user} - Action: {action_type} - Level: {kpilevel}")
            
            if action_type == 'goal':
                menu_id = GOAL_APPROVE_MENU
            elif action_type == 'kpi':
                menu_id = KPI_APPROVE_MENU
            else:
                menu_id = PERFORMANCE_CONFIG_MENU
            User_menu_access.objects.get_or_create(user=assigned_user, dynamic_menu_id=menu_id)

        def create_kpi_approval_status(user, user_year, user_tp, perm, action_type, kpilevel_id=1):
            if action_type == 'goal':
                KPIApprovalStatus.objects.get_or_create(
                    approved_rejected_user=user,
                    added_by=perm.assigned_to,
                    kpilevel_id=kpilevel_id,
                    kpi_user_year=user_year,
                    action_type=action_type,
                    defaults={
                        'is_approve': False,
                        'is_active': False,
                        'approval_status': 'pending'
                    }
                )
                insert_log(f"Created/checked KPIApprovalStatus for GOAL: user {user.first_name} - {user.last_name}, level {kpilevel_id}, added_by {perm.assigned_to}")
            elif action_type == 'kpi' and user_tp:
                KPIApprovalStatus.objects.get_or_create(
                    approved_rejected_user=user,
                    added_by=perm.assigned_to,
                    kpilevel_id=kpilevel_id,
                    kpiuser_timeperiod=user_tp,
                    action_type=action_type,
                    defaults={
                        'is_approve': False,
                        'is_active': False,
                        'approval_status': 'pending'
                    }
                )
                insert_log(f"Created/checked KPIApprovalStatus for KPI: user {user.first_name} - {user.last_name}, level {kpilevel_id}, added_by {perm.assigned_to}, period {user_tp.kpi_time_period.name}")

        # insert_log(f"Processing sheet: {ws.title} with Employee Code: {emp_code}")
        period_mappings = get_period_mappings(ws)
        insert_log(f"Period mappings found: {period_mappings}")
        if not emp_code:
            insert_log("Employee Code in B1 is missing. Skipping sheet.")
            continue
                
        # Find the header row (assume always row 2 as per your sample)
        original_header = [str(cell.value).strip() for cell in ws[2]]
        header = fix_duplicate_headers(original_header, period_mappings)
        insert_log(f"Fixed header: {header}")
        # Create case-insensitive column mapping
        col_map = {}
        col_map_lower = {}
        for idx, name in enumerate(header):
            col_map[name] = idx
            col_map_lower[name.lower()] = idx

        # Helper function to find column case-insensitively
        def find_column(col_name):
            if col_name in col_map:
                return col_name
            elif col_name.lower() in col_map_lower:
                # Find the original column name
                for orig_name in header:
                    if orig_name.lower() == col_name.lower():
                        return orig_name
            return None

        # Check for required columns (case-insensitive)
        required_cols = ['Name', 'Department', 'Title', 'KPI', 'Goal', 'Formula', f'{year_name} Goal', 'Weight']
        for col in required_cols:
            found_col = find_column(col)
            if not found_col:
                insert_log(f"Missing column in sheet '{ws.title}': {col}")
                continue
                # raise Exception(f"Missing column in sheet '{ws.title}': {col}")

        # Find all Target/Actual/Achievement sets dynamically (case-insensitive)
        period_specs = []
        for name in header:
            name_lower = name.lower()
            if name_lower.startswith('target - ') or name.startswith('Target - '):
                period = name.replace('Target - ', '').replace('target - ', '').strip()
                tgt = name

                # Look for corresponding Actual and Achievement columns (case-insensitive)
                act_variations = [f'Actual - {period}', f'actual - {period}']
                ach_variations = [f'{period} Achievement', f'{period} achievement', 'Achievement', 'achievement']

                act_col = None
                ach_col = None

                # Find actual column
                for act_var in act_variations:
                    if find_column(act_var):
                        act_col = find_column(act_var)
                        break

                # Find achievement column
                for ach_var in ach_variations:
                    if find_column(ach_var):
                        ach_col = find_column(ach_var)
                        break

                if act_col and ach_col:
                    period_specs.append({
                        'period_name': period,
                        'target_col': tgt,
                        'actual_col': act_col,
                        'ach_col': ach_col
                    })

        with transaction.atomic():
            # Collect all rows first to preserve order
            data_rows = list(ws.iter_rows(min_row=3, values_only=True))
            processed_users = set()
            user_time_period_scores = {}
            sheet_started = False  # Track if we've logged the sheet start message
            for row_idx, row in enumerate(data_rows, start=3):
                insert_log(f"\n---- Employee iteration: Row {row_idx} ----")

                # Skip TOTAL row and rows where KPI is 'TOTAL' or Goal is 'TOTAL' or Goal is empty
                kpi_col = find_column('KPI')
                goal_col = find_column('Goal')
                kpi_val = row[col_map[kpi_col]] if kpi_col else None
                goal_val = row[col_map[goal_col]] if goal_col else None
                if (
                    (row[0] and str(row[0]).strip().upper() == 'TOTAL') or
                    (kpi_val and str(kpi_val).strip().upper() == 'TOTAL') or
                    (goal_val and str(goal_val).strip().upper() == 'TOTAL') or
                    (goal_val is None or str(goal_val).strip() == '')
                ):
                    continue

                try:
                    user = None
                    try:
                        user = User.objects.get(profile__employee_id=emp_code)
                    except User.DoesNotExist:
                        normalized_emp_code = str(emp_code).lstrip('0') if emp_code else ''
                        try:
                            user = User.objects.get(profile__employee_id__regex=f'^0*{normalized_emp_code}$')
                        except User.DoesNotExist:
                            insert_log(f"User with Employee ID {emp_code} not found. Skipping row {row_idx}.")
                            continue
                except User.DoesNotExist:
                    insert_log(f"User with Employee ID {emp_code} not found. Skipping row {row_idx}.")
                    continue


                # Log sheet start message only once per sheet
                if not sheet_started:
                    insert_log(f"\n\n===== START SHEET: '{sheet_name}' (EmpCode: {emp_code}) =====")
                    sheet_started = True

                insert_log(f"Looking up User with EmpID={emp_code} → {user.get_full_name()}")

                # Get column names case-insensitively
                dept_col = find_column('Department')
                kpi_col = find_column('KPI')
                goal_col = find_column('Goal')
                formula_col = find_column('Formula')
                year_goal_col = find_column(f'{year_name} Goal')
                dept_name = str(row[col_map[dept_col]]).strip() if dept_col and row[col_map[dept_col]] else ''
                kpi_name = str(row[col_map[kpi_col]]).strip() if kpi_col and row[col_map[kpi_col]] else ''
                goal_name = str(row[col_map[goal_col]]).strip() if goal_col and row[col_map[goal_col]] else ''
                insert_log(f"Processing KPI: '{kpi_name}', Goal: '{goal_name}'")
                measurement_name = str(row[col_map[formula_col]]).strip() if formula_col and row[col_map[formula_col]] else ''
                remarks_col = find_column('Remarks')
                if not year_goal_col:
                    alternative_formats = [
                        f'{year_name} goal',
                        f'{year_name}Goal',
                        f'{year_name} target',
                        f'{year_name}',
                        'Goal'
                    ]
                    for alt_format in alternative_formats:
                        year_goal_col = find_column(alt_format)
                        if year_goal_col:
                            insert_log(f"Using alternative column '{year_goal_col}' for '{year_name} Goal'")
                            break

                if year_goal_col:
                    year_goal_value_raw = row[col_map[year_goal_col]]
                    insert_log(f"Raw '{year_name} Goal' value: {year_goal_value_raw}")
                    
                    # Improved handling for different value formats
                    if year_goal_value_raw is None or year_goal_value_raw == '':
                        year_goal_value = 0
                    elif isinstance(year_goal_value_raw, (int, float)):
                        if 0 < year_goal_value_raw <= 1:
                            year_goal_value = f"{int(year_goal_value_raw * 100)}%"
                            insert_log(f"Converting decimal {year_goal_value_raw} to percentage: {year_goal_value}")
                        else:
                            year_goal_value = year_goal_value_raw
                    elif isinstance(year_goal_value_raw, str):
                        if '%' in year_goal_value_raw:
                            # Keep percentage values as-is
                            year_goal_value = year_goal_value_raw
                            insert_log(f"Storing percentage value as-is: {year_goal_value}")
                        else:
                            # Handle string values, including those with commas, currency symbols, etc.
                            clean_value = year_goal_value_raw.replace(',', '').replace('$', '').replace('%', '')
                            try:
                                year_goal_value = float(clean_value)
                            except ValueError:
                                insert_log(f"Could not convert '{year_goal_value_raw}' to number. Using as text.")
                                year_goal_value = year_goal_value_raw
                    else:
                        year_goal_value = year_goal_value_raw
                else:
                    insert_log(f"No column found for '{year_name} Goal'. Using default value 0.")
                    year_goal_value = 0
                max_finish_rate_col = find_column('Max Finish Rate')
                weight_col = find_column('Weight')
                max_finish_rate = row[col_map[max_finish_rate_col]] if max_finish_rate_col and row[col_map[max_finish_rate_col]] not in (None, '') else 130
                # Parse weight as float from sheet (remove % and spaces)
                weight_raw = row[col_map[weight_col]] if weight_col else None
                if isinstance(weight_raw, str):
                    weight_clean = weight_raw.replace('%','').replace(' ','').replace(',','.')
                    weight_value = float(weight_clean)
                elif isinstance(weight_raw, (int, float)):
                    weight_value = float(weight_raw) * 100
                else:
                    weight_value = 0
                # Skip rows with missing required values like weight or goal
                if not weight_value:
                    insert_log(f"Skipping row {row_idx} due to missing goal or weight.")
                    continue
                
                # Check if this exact KPI-Goal combination already exists
                existing_kpi_goal = KpiCategory.objects.filter(
                    name=kpi_name,department=user.profile.department,is_active=True, kpi_goals__name=goal_name,kpi_goals__is_active=True
                ).first()
                if existing_kpi_goal:
                    # Exact combination exists, use it
                    kpi_cat = existing_kpi_goal
                    goal = KpiGoal.objects.filter(
                        name=goal_name,
                        kpi_category=kpi_cat,
                        is_active=True
                    ).first()
                    insert_log(f"Using existing KPI-Goal combination: KPI(id={kpi_cat.id}) with Goal(id={goal.id})")
                else:
                    # Always create a new KPI category for every new goal, even if KPI name is the same
                    kpi_cat = KpiCategory.objects.create(
                        name=kpi_name,
                        department=user.profile.department,
                        is_active=True
                    )
                    insert_log(f"Created new KpiCategory(id={kpi_cat.id}, name='{kpi_name}') for goal '{goal_name}'")

                # Create KpiCategory
                formula_col = find_column('Formula')
                raw_formula = str(row[col_map[formula_col]]).strip() if formula_col and row[col_map[formula_col]] else ''
                clean_formula = raw_formula.lstrip('=').strip()  # Remove '=' and leading/trailing spaces
                normalized_formula = normalize_formula(clean_formula)
                measurement = None
                try:
                    measurement =  MeasurementType.objects.filter(Q(calculation=normalized_formula) | Q(calculation__iexact=normalized_formula),is_active=True).first()
                except MeasurementType.DoesNotExist:
                    insert_log(f"MeasurementType '{measurement_name}' not found. Skipping row {row_idx} for user {emp_code}.")
                    continue
                if not measurement:
                    # If no formula provided, use a default measurement type
                    measurement = MeasurementType.objects.filter(name__icontains='Actual / Target', is_active=True).first()
                # Create KpiGoal
                try:
                    hr_user = User.objects.get(id=360)
                except User.DoesNotExist:
                    insert_log("User '360 Najma Sahar' not found. Cannot assign as added_by for KpiGoal.")
                    continue
                existing_goal = KpiGoal.objects.filter(
                    name=goal_name,
                    kpi_category=kpi_cat,
                    is_active=True
                ).first()
                if existing_goal:
                    goal = existing_goal
                    if goal.measurement_type != measurement:
                        goal.measurement_type = measurement
                        goal.save(update_fields=['measurement_type'])
                        insert_log(f"Updated measurement type for existing goal '{goal_name}' to {measurement.name} (id={measurement.id})")
                else:
                    goal = KpiGoal.objects.create(
                        name=goal_name,
                        kpi_category=kpi_cat,
                        measurement_type=measurement,
                        added_by=hr_user,
                        is_active=True
                    )
                    insert_log(f"Created new KpiGoal: {goal_name} for KPI {kpi_name}")
                # goal, goal_created = KpiGoal.objects.get_or_create(
                #     name=goal_name,
                #     kpi_category=kpi_cat,
                #     added_by = hr_user,
                #     is_active=True,
                #     defaults={'measurement_type': measurement}
                # )
                # if goal_created:
                #     insert_log(f"Created KpiGoal: {goal_name} for KPI {kpi_name}")

                # Create KpiUserYear
                user_year, user_year_created = KpiUserYear.objects.get_or_create(
                    user=user,
                    kpi_year=kpi_year,
                    defaults={'status': 1}
                )
                KpiUserYear.objects.filter(id=user_year.id).update(status=1)
                if user_year_created:
                    insert_log(f"Created KpiUserYear for user {user.first_name} - {user.last_name} and year {kpi_year.name}")
                
                # Create KpiUserGoal
                kug, kug_created = KpiUserGoal.objects.get_or_create(
                    kpi_user_year=user_year,
                    kpi_goal=goal,
                    defaults={
                        'goal_value': year_goal_value,
                        'max_finish_rate': max_finish_rate,
                        'weight': weight_value,
                        'added_by': user,
                        'is_active': True
                    }
                )
                if kug_created:
                    insert_log(f"Created KpiUserGoal for user {user.first_name} - {user.last_name} and goal {goal_name}")
                else:
                    if not kug_created:
                        # Optionally update fields if needed
                        kug.goal_value = year_goal_value
                        kug.max_finish_rate = max_finish_rate
                        kug.weight = weight_value
                        kug.added_by = user
                        kug.is_active = True
                        kug.save()

                # Process periods
                user_time_periods = []
                for spec in period_specs:
                    tgt_val_raw = row[col_map[spec['target_col']]] if spec['target_col'] and spec['target_col'] in col_map else None
                    act_val_raw = row[col_map[spec['actual_col']]] if spec['actual_col'] and spec['actual_col'] in col_map else None
                    tgt_val = parse_percentage_value(tgt_val_raw)
                    act_val = parse_percentage_value(act_val_raw)
                    if tgt_val in (None, '') or act_val in (None, ''):
                        continue

                    # Process and store KPIUserTimePeriod and KPIUserGoalValues
                    period_obj = KpiTimePeriod.objects.filter(kpi_year=kpi_year, name=spec['period_name'], is_active=True).first()
                    if not period_obj:
                        insert_log(f"KpiTimePeriod '{spec['period_name']}' not found for year {kpi_year.name}. Skipping period.")
                        continue

                    # Calculate achievement
                    ach_val = calculate_achievement(act_val, tgt_val, measurement.calculation, max_finish_rate, weight_value)
                    ach_val_rounded = round(ach_val)  # Store as integer percentage
                    score_key = (user_year.id, period_obj.id)
                    user_tp, user_tp_created = KpiUserTimePeriod.objects.get_or_create(
                        kpi_user_year=user_year,
                        kpi_time_period=period_obj,
                        defaults={
                            'score': ach_val_rounded,
                            'kpi_approval_status': 'pending'
                        }
                    )
                    if score_key not in user_time_period_scores:
                        user_time_period_scores[score_key] = 0
                    user_time_period_scores[score_key] += ach_val_rounded
                    
                    # Update the score
                    user_tp.score = user_time_period_scores[score_key]
                    user_tp.save(update_fields=['score'])
                    
                    user_time_periods.append(user_tp)
                    # if not user_tp_created:
                    #     user_tp.score = (user_tp.score or 0) + ach_val_rounded
                    #     user_tp.save(update_fields=['score'])
                    # user_time_periods.append(user_tp)
                    if user_tp_created:
                        insert_log(f"Created KpiUserTimePeriod for user {user.first_name} - {user.last_name}, period {spec['period_name']}, score: {ach_val_rounded}")
                    else:
                        insert_log(f"Updated KpiUserTimePeriod score for user {user.first_name} - {user.last_name}, period {spec['period_name']}, new score: {user_tp.score}")

                    # Find remark column (immediately after achievement columns)
                    ach_col_idx = col_map[spec['ach_col']] if spec['ach_col'] in col_map else None
                    remark_val = None
                    if ach_col_idx is not None:
                        remarks_value = row[col_map[remarks_col]] if remarks_col and col_map[remarks_col] < len(row) else None
                        remarks_str = str(remarks_value) if remarks_value is not None else ""
                    # Create KpiUserActual record
                    KpiUserActual.objects.update_or_create(
                        kpi_user_time_period=user_tp,
                        kpi_user_goal=kug,
                        defaults={
                            'actual': act_val,
                            'remark': remarks_str,
                            'added_by': user
                        }
                    )

                    # Create KpiUserTarget record 
                    KpiUserTarget.objects.update_or_create(
                        kpi_user_time_period=user_tp,
                        kpi_user_goal=kug,
                        defaults={
                            'target': tgt_val,
                            'added_by': user
                        }
                    )

                    # Create KPIUserGoalValues record to maintain consistency
                    KPIUserGoalValues.objects.update_or_create(
                        kpiuser_goal=kug,
                        kpiuser_timeperiod=user_tp,
                        defaults={
                            'goal_value': year_goal_value,
                            'max_finish_rate': max_finish_rate,
                            'weight': weight_value,
                            'target': tgt_val,
                            'actual': act_val,
                            'remark': remarks_str
                        }
                        )
                    insert_log(f"Processed period {spec['period_name']} for user {user.first_name} - {user.last_name}, goal {goal_name}, target {tgt_val}, actual {act_val}, achievement {ach_val_rounded}")

                if user.id not in processed_users:
                    processed_users.add(user.id)
                    
                    checklist = ['kpi_settings','kpi_goal','goal_settings','target_settings','actual_settings']
                    action_perms = KpiActionPermission.objects.select_related('user','kpi_action').filter(kpi_action__key_action__in=checklist, user=user)
                    # Only process KPILevelPermissions and KPIApprovalStatus if KpiActionPermission exists for this user
                    if action_perms.exists():
                        for perm in action_perms:
                            # Create level permissions
                            for action in ('goal', 'kpi'):
                                create_kpi_level_permissions(user, perm.assigned_to, action, kpi_level)
                            # Create approval status for goal (once per user-year)
                            create_kpi_approval_status(user, user_year, None, perm, 'goal', kpi_level)
                            # Create approval status for each time period
                            for user_tp in user_time_periods:
                                create_kpi_approval_status(user, None, user_tp, perm, 'kpi', kpi_level)
                insert_log(f"--- Finished processing user '{user.first_name} - {user.last_name}' ---\n")

            # Add separator at the end of sheet processing (without timestamp)
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write("=" * 60 + "\n")

    # After processing all Excel rows, ensure KPILevelPermission is created for all users in KpiActionPermission
    checklist = ['kpi_settings','kpi_goal','goal_settings','target_settings','actual_settings']
    action_perms = KpiActionPermission.objects.select_related('user','kpi_action').filter(kpi_action__key_action__in=checklist)
    for perm in action_perms:
        # Skip if user is None (some KpiActionPermission records have null user)
        if perm.user is not None:
            for action in ('goal', 'kpi'):
                create_kpi_level_permissions(perm.user, perm.assigned_to, action, kpi_level)

    insert_log("KPI import completed.")
    print("KPI import completed.")


def import_kpi_goals_from_folder(folder_path, year_name):
    """
    Iterate over all Excel files in the given folder and import them.
    """
    excel_files = glob.glob(os.path.join(folder_path, "*.xlsx"))
    if not excel_files:
        print(f"No Excel files found in {folder_path}")
        return
    for file in excel_files:
        print(f"Importing: {file}")
        import_kpi_goals_from_excel(filepath=file, year_name=year_name)

def update_user_department_22_to_24():
    """
    Update user profiles with department ID 22 to department ID 24
    """
    try:
        with transaction.atomic():
            # Check if department 24 exists
            try:
                new_department = Department.objects.get(id=24)
                print(f"Target department found: {new_department.name} (ID: 24)")
            except Department.DoesNotExist:
                print("Error: Department with ID 24 does not exist.")
                return

            # Check if department 22 exists
            try:
                old_department = Department.objects.get(id=22)
                print(f"Source department found: {old_department.name} (ID: 22)")
            except Department.DoesNotExist:
                print("Error: Department with ID 22 does not exist.")
                return

            # Find all user profiles with department ID 22
            profiles_to_update = Profile.objects.filter(department_id=22)
            count = profiles_to_update.count()

            if count == 0:
                print("No user profiles found with department ID 22.")
                return

            print(f"Found {count} user profile(s) with department ID 22.")

            # Update the profiles
            updated_count = profiles_to_update.update(department_id=24)

            print(f"Successfully updated {updated_count} user profile(s) from department ID 22 to 24.")

            # Log the updated users
            updated_profiles = Profile.objects.filter(department_id=24, user__isnull=False)
            print("\nUpdated user profiles:")
            for profile in updated_profiles:
                if profile.user:
                    print(f"  - {profile.user.first_name} {profile.user.last_name} (Employee ID: {profile.employee_id})")

    except Exception as e:
        print(f"Error updating user departments: {e}")
        traceback.print_exc()

def update_dynamic_menu_122_role_access():
    """
    Update dynamic menu with ID 122 to set is_role_access = True
    """
    try:
        with transaction.atomic():
            # Check if dynamic menu with ID 122 exists
            try:
                dynamic_menu = Dynamic_menus.objects.get(id=122)
                print(f"Dynamic menu found: {dynamic_menu} (ID: 122)")

                # Update is_role_access to True
                dynamic_menu.is_role_access = True
                dynamic_menu.save(update_fields=['is_role_access'])

                print(f"Successfully updated dynamic menu ID 122 - is_role_access set to True")

            except Dynamic_menus.DoesNotExist:
                print("Error: Dynamic menu with ID 122 does not exist.")
                return

    except Exception as e:
        print(f"Error updating dynamic menu: {e}")
        traceback.print_exc()

def update_kpi_action_permissions_to_user_326():
    """
    For all active, non-superuser users:
    1. Delete all existing KPIActionPermission records
    2. Add new KPIActionPermission records with assigned_to as user ID 326
    """
    try:
        with transaction.atomic():
            # Check if user 326 exists
            try:
                assigned_user = User.objects.get(id=326)
                print(f"Target assigned_to user found: {assigned_user.first_name} {assigned_user.last_name} (ID: 326)")
            except User.DoesNotExist:
                print("Error: User with ID 326 does not exist.")
                return

            # Get all active, non-superuser users
            users = User.objects.select_related('profile__department').filter(is_active=True,profile__employement_type = 2).exclude(is_superuser=True)
            user_count = users.count()

            if user_count == 0:
                print("No active, non-superuser users found.")
                return

            print(f"Found {user_count} active, non-superuser users.")

            # Get all KPI actions
            kpi_actions = KpiAction.objects.filter(is_active=True,key_action__in=['goal_settings','target_settings','actual_settings']).order_by('id')
            if not kpi_actions.exists():
                print("No active KPI actions found.")
                return

            print(f"Found {kpi_actions.count()} active KPI actions.")

            # First, delete KPIActionPermission records with blank/null user_id
            blank_permissions_count = KpiActionPermission.objects.filter(user__isnull=True).count()
            KpiActionPermission.objects.filter(user__isnull=True).delete()
            print(f"Deleted {blank_permissions_count} KPIActionPermission records with blank user_id")

            # Process each user
            processed_count = 0

            for user in users:
                # Delete existing KPIActionPermission records for this user
                delete_result = KpiActionPermission.objects.filter(user=user).delete()
                deleted_count = delete_result[0]
                print(f"  - Deleted {deleted_count} existing permission(s) for user {user.first_name} {user.last_name}")

                # Create new KPIActionPermission records with assigned_to as user 326
                new_permissions = []
                for action in kpi_actions:
                    new_permissions.append(
                        KpiActionPermission(
                            user=user,
                            assigned_to=assigned_user,
                            kpi_action=action,
                            department=user.profile.department
                        )
                    )

                # Bulk create the new permissions
                if new_permissions:
                    KpiActionPermission.objects.bulk_create(new_permissions)

                processed_count += 1
                if processed_count % 10 == 0:  # Print progress every 10 users
                    print(f"Processed {processed_count}/{user_count} users...")

            print(f"\nSuccessfully processed {user_count} users:")
            print(f"- Deleted {blank_permissions_count} KPIActionPermission records with blank user_id")
            print(f"- Updated {processed_count} users with new permissions")
            print(f"- Deleted existing KPIActionPermission records for all users")
            print(f"- Created new KPIActionPermission records with assigned_to user ID 326")

    except Exception as e:
        print(f"Error updating KPI action permissions: {e}")
        traceback.print_exc()

def delete_all_kpi_master_data():
    """
    Delete all KPI master table records including:
    - KpiCategory
    - KpiGoal
    - KpiUserYear and all related records
    - KpiUserGoal
    - KpiUserTimePeriod
    - KPIUserGoalValues
    - KPIApprovalStatus
    - KPILevelPermissions
    - KpiUserActual
    - KpiUserTarget
    - User_menu_access (KPI related)
    - KPIExportLog
    """
    try:
        with transaction.atomic():
            print("Starting deletion of all KPI master data...")

            # Delete in proper order to avoid foreign key constraints

            # 1. Delete KPIUserGoalValues first (depends on KpiUserGoal and KpiUserTimePeriod)
            kpi_goal_values_count = KPIUserGoalValues.objects.count()
            KPIUserGoalValues.objects.all().delete()
            print(f"✓ Deleted {kpi_goal_values_count} KPIUserGoalValues records")

            # 2. Delete KpiUserActual and KpiUserTarget (depends on KpiUserTimePeriod and KpiUserGoal)
            actual_count = KpiUserActual.objects.count()
            KpiUserActual.objects.all().delete()
            print(f"✓ Deleted {actual_count} KpiUserActual records")

            target_count = KpiUserTarget.objects.count()
            KpiUserTarget.objects.all().delete()
            print(f"✓ Deleted {target_count} KpiUserTarget records")

            # 3. Delete KPIApprovalStatus (depends on KpiUserYear and KpiUserTimePeriod)
            approval_status_count = KPIApprovalStatus.objects.count()
            KPIApprovalStatus.objects.all().delete()
            print(f"✓ Deleted {approval_status_count} KPIApprovalStatus records")

            # 4. Delete KPILevelPermissions
            level_permissions_count = KPILevelPermissions.objects.count()
            KPILevelPermissions.objects.all().delete()
            print(f"✓ Deleted {level_permissions_count} KPILevelPermissions records")

            # 5. Delete KpiUserTimePeriod (depends on KpiUserYear)
            user_time_period_count = KpiUserTimePeriod.objects.count()
            KpiUserTimePeriod.objects.all().delete()
            print(f"✓ Deleted {user_time_period_count} KpiUserTimePeriod records")

            # 6. Delete KpiUserGoal (depends on KpiUserYear and KpiGoal)
            user_goal_count = KpiUserGoal.objects.count()
            KpiUserGoal.objects.all().delete()
            print(f"✓ Deleted {user_goal_count} KpiUserGoal records")

            # 7. Delete KpiUserYear
            user_year_count = KpiUserYear.objects.count()
            KpiUserYear.objects.all().delete()
            print(f"✓ Deleted {user_year_count} KpiUserYear records")

            # 8. Delete KpiGoal (depends on KpiCategory)
            goal_count = KpiGoal.objects.count()
            KpiGoal.objects.all().delete()
            print(f"✓ Deleted {goal_count} KpiGoal records")

            # 9. Delete KpiCategory
            category_count = KpiCategory.objects.count()
            KpiCategory.objects.all().delete()
            print(f"✓ Deleted {category_count} KpiCategory records")

            # # 10. Delete KPI-related User_menu_access entries
            menu_access_count = User_menu_access.objects.filter(
                dynamic_menu_id__in=[PERFORMANCE_CONFIG_MENU, KPI_APPROVE_MENU, GOAL_APPROVE_MENU]
            ).count()
            User_menu_access.objects.filter(
                dynamic_menu_id__in=[PERFORMANCE_CONFIG_MENU, KPI_APPROVE_MENU, GOAL_APPROVE_MENU]
            ).delete()
            print(f"✓ Deleted {menu_access_count} KPI-related User_menu_access records")

            # 11. Delete KPIExportLog
            export_log_count = KPIExportLog.objects.count()
            KPIExportLog.objects.all().delete()
            print(f"✓ Deleted {export_log_count} KPIExportLog records")

            print("\n🎉 Successfully deleted all KPI master data!")
            print("⚠️  Note: KpiYear, KpiTimePeriod, MeasurementType, and KpiAction master tables were preserved.")

    except Exception as e:
        print(f"❌ Error deleting KPI master data: {e}")
        traceback.print_exc()

# # Command line argument support
if __name__ == "__main__":
    available_functions = {
        "import_folder": import_kpi_goals_from_folder,
        "delete_all_2024_kpi": lambda: [delete_kpi_user_year_and_related(user_year.id) for user_year in KpiUserYear.objects.filter(kpi_year__name__icontains='2024')],
        "update_dept_22_to_24": update_user_department_22_to_24,
        "update_role_access":update_dynamic_menu_122_role_access,
        "kpi_action_permissions":update_kpi_action_permissions_to_user_326,
        "delete_all_kpi_master":delete_all_kpi_master_data,
    }
    if len(sys.argv) <= 1 or sys.argv[1] in ['-h', '--help']:
        for func_name in available_functions:
            print(f"  {func_name}")
        sys.exit(0)
    
    # Get function name from first argument
    func_name = sys.argv[1]
    
    # Check if function exists
    if func_name not in available_functions:
        print(f"Error: Function '{func_name}' not found. Available functions: {', '.join(available_functions.keys())}")
        sys.exit(1)
    
    # Get the function
    func = available_functions[func_name]
    
    # Call the function with remaining arguments
    try:
        if func_name == "import_folder":
            folder_path = sys.argv[2] if len(sys.argv) > 2 else "media/2024_kpi"
            year_name = sys.argv[3] if len(sys.argv) > 3 else "2024"
            import_kpi_goals_from_folder(folder_path=folder_path, year_name=year_name)
        
        
        elif func_name == "delete_kpi":
            if len(sys.argv) <= 2:
                print("Error: Missing user_year_id for delete_kpi")
                sys.exit(1)
            user_year_id = int(sys.argv[2])
            remark = sys.argv[3] if len(sys.argv) > 3 else None
            delete_kpi_user_year_and_related(user_year_id, remark)
        
        else:
            # For simple functions with no arguments
            func()
        
        print(f"Successfully executed {func_name}")
    
    except Exception as e:
        print(f"Error executing {func_name}: {e}")
        traceback.print_exc()
        sys.exit(1)

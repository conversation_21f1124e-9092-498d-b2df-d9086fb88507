from decimal import Decimal
import re
from bs4 import BeautifulSoup
import openpyxl
from hisensehr.models import KpiAction,KpiActionPermission,KpiCategory,Department, KpiYear, KpiTimePeriod, KpiGoal, MeasurementType,KpiUserGoal,KpiUserTimePeriod,\
    KpiUserActual,KpiUserTarget,HeadOfDepartment,User,KpiUserYear, KpiActualTargetPermission,\
    PerformanceModificationLog,Department
from django.views import View
from hisensehr.helper import *
from django.db import transaction
from dateutil import relativedelta
from django.http import JsonResponse
from django.db.models.functions import Concat,Lower
from django.db.models import Prefetch,Sum,Value,CharField,Subquery,OuterRef
import json
from django.template import loader
from datetime import date,datetime
from django.contrib.auth.mixins import PermissionRequiredMixin
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied,ValidationError
from django.contrib.sites.shortcuts import get_current_site
from django.http import HttpResponse
from hisensehr.templatetags.my_filters import get_achieved_percentage, get_weightage_score
from weasyprint import HTML,CSS
from hisenseapi.helper import notificationUpdate
from hisensehr.tasks import send_email_to_users
from django.http import Http404
from hisensehr.forms.main import GuidelineForm, UserGuidelineForm
from django.core.exceptions import ObjectDoesNotExist
import base64
from openpyxl import Workbook
# from bs4 import BeautifulSoup
from django.http import HttpResponse
from openpyxl.styles import Alignment, Border, Side, Protection,Font
from django.utils import timezone
from django.db.models import Value as V
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from ..constantvariables import APPROVED_STATUS, INPROGRESS_STATUS, KPI_APPROVE_MENU, PENDING_STATUS, REQUESTS_KPI_MAIN_MENU,GOAL_APPROVE_MENU,PERFORMANCE_CONFIG_MENU
from django.http import HttpResponseRedirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import F,Count,Exists,OuterRef
import ast
from openpyxl.styles import Border, Side
from openpyxl import load_workbook 
from openpyxl.worksheet.datavalidation import DataValidation
import traceback
from django.utils import timezone as datetime_timezone

class TimePeriodView(LoginRequiredMixin,View):
    login_url = '/'
    def has_permission(self):
        # Check if the user has access to the dynamic menu
        import_export_name = 'kpi-time-periods'
        menu = Dynamic_menus.objects.filter(title_slug=import_export_name,is_active=True).last()
        if menu:
            menu_id = menu.id
            user_groups = self.request.user.groups.values_list('id', flat=True)[0]
            has_access = Group_menu_access.objects.filter(group_id=user_groups, dynamic_menu_id=menu_id).exists()
            return True if has_access else False
        else:
            return False
        
    def get(self,request,*args, **kwargs):
        data = {}
        has_permission = False
        encrypt = request.GET.get('encrypt', None)
        if encrypt:
            data['encrypt'] = encrypt
                
        data['is_hod'] = HeadOfDepartment.objects.select_related('user').filter(user=request.user,is_active=True).exists()
        data['has_perm']  = KpiActionPermission.objects.select_related('assigned_to','kpi_action','user').filter(assigned_to=request.user,kpi_action__key_action__in=['target_settings','actual_settings'])
        userlist = list(set([usr.user_id for usr in data['has_perm']]))
        data['userlist'] = userlist
        kpi_level_user_ids = list(set(KPILevelPermissions.objects.select_related('user_id').filter(
            user_id__in=userlist, is_active=True,action_type=KPI
        ).values_list('user_id', flat=True).distinct()))
        kpi_count = len(kpi_level_user_ids)
        users_not_in_kpi_level = list(set(userlist) - set(kpi_level_user_ids))
        users_not_kpi = User.objects.filter(id__in=users_not_in_kpi_level)
        print(self.has_permission(),'----permission access')
        if is_admin_user(request.user) or self.has_permission(): 
            data['departments'] = Department.objects.filter(is_active=True).order_by(Lower("name"))
            data['has_perm']  = KpiActionPermission.objects.select_related('assigned_to','kpi_action','user').filter(kpi_action__key_action__in=['target_settings','actual_settings'])
            data['userlist'] = list(set([usr.user_id for usr in data['has_perm']]))
            data['user_count'] = len(data['userlist'])
            has_permission = True
        elif data['has_perm']:
            # Users with KPIActionPermission can see their assigned departments
            assigned_dept_ids = data['has_perm'].values_list('department_id', flat=True).distinct()
            data['departments'] = Department.objects.filter(id__in=assigned_dept_ids, is_active=True).order_by(Lower("name"))
            data['user_count'] = len(userlist)
            has_permission = True
        # data['user_count'] = len(userlist)
        if len(userlist) == kpi_count:
            data['kpi_permission'] = 1
            data['users_not_kpi'] = None
        else:
            data['kpi_permission'] = 0
            data['users_not_kpi'] = ', '.join([users_not_kpi.first_name+ ' ' + users_not_kpi.last_name for users_not_kpi in users_not_kpi])
        can_approve = False
        if data['is_hod']:
            can_approve = True
        # if data['has_perm']:
        #     has_permission = True
        if  has_permission or can_approve:
            data['has_perm'] = has_permission

            # For superusers, show all user records; for others, show only their own records
            if request.user.is_superuser:
                kpi_user_filter = {}  # No filter - show all users
            else:
                kpi_user_filter = {'kpi_user_year__user': request.user}

            data['time_periods'] = KpiTimePeriod.objects.select_related('kpi_year').filter(
                kpi_year__is_active=True, kpi_year__current_year=True, is_active=True
            ).prefetch_related(
                Prefetch(
                    'kpiusertimeperiod_set',
                    queryset=KpiUserTimePeriod.objects.select_related('kpi_user_year', 'kpi_user_year__user').filter(
                        **kpi_user_filter
                    ).order_by('id'),
                    to_attr='kpi_userperms'
                )
            ).order_by('id')
            return renderfile(request,'kpi/overview','time_period_list',data)
        else:
            raise PermissionDenied()



class MyKpiView(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpiusertarget'    
    def get(self,request,*args, **kwargs):
        data,context = {},{}
        context['status'] = False
        selected_year = request.GET.get('year',None)
        try:
            if selected_year:
                selected_year = int(selected_year)
        except ValueError:
            selected_year = None
        context['year'] = KpiYear.objects.filter(is_active=True).order_by('-start_date')
        if selected_year:
            context['time_periods'] = KpiTimePeriod.objects.filter(kpi_year=selected_year).order_by('id').\
                prefetch_related(Prefetch('kpiusertimeperiod_set',queryset=KpiUserTimePeriod.objects.filter(kpi_user_year__user=request.user).order_by('id')))
        else:
            context['time_periods'] = KpiTimePeriod.objects.filter(kpi_year__is_active=True,kpi_year__current_year=True,).\
                prefetch_related(Prefetch('kpiusertimeperiod_set',queryset=KpiUserTimePeriod.objects.filter(kpi_user_year__user=request.user).order_by('id')))
        if request.headers.get("x-requested-with") == "XMLHttpRequest" : 
            data["status"] = True
            data["template"] = render_to_string("hisensehr/kpi/my_kpi_ajax.html", context, request)
            return JsonResponse(data)
        if KpiUserTimePeriod.objects.filter(kpi_user_year__user=request.user,kpi_approval_status='approved'):
            data['status'] = True
        return renderfile(request,'kpi','my_kpi',context)


class GoalSetView(LoginRequiredMixin,View):
    login_url = '/'
    def get(self,request,*args, **kwargs):
        if KpiActionPermission.objects.select_related('assigned_to','kpi_action').filter(assigned_to=request.user,kpi_action__key_action='goal_settings').exists():
            data = {}
            encrypt = request.GET.get('encrypt', None)
            search_key = request.GET.get('search_key', None)
            department_id = request.GET.get('department', None)    
            decrypted = None
            if encrypt:
                try:
                    decrypted = decrypt_me(encrypt)
                    data['decrypted'] = decrypted
                except:
                    raise Http404()
                
            # data['time_periods'] = KpiYear.objects.filter(is_active=True)
            perm_added_user_ids = KpiActionPermission.objects.select_related('assigned_to','kpi_action','user_id').filter(assigned_to=request.user,kpi_action__key_action='goal_settings').exclude(user_id__isnull=True).values_list('user_id',flat=True).distinct()
            data['categories'] = KpiCategory.objects.filter(is_active=True).prefetch_related(Prefetch('kpi_goals',queryset=KpiGoal.objects.filter(is_active=True).select_related('measurement_type').order_by('id')))
            data['goals'] = KpiGoal.objects.filter(is_active=True).select_related()
            userData = User.objects.select_related('profile','profile__designation','profile__department').filter(id__in=perm_added_user_ids,is_active=True).order_by(Lower("first_name"))
            kpiuseryearDat = KpiYear.objects.filter(is_active=True,current_year=True).last()
            user_goals = KpiUserGoal.objects.select_related(
                'kpi_user_year', 'kpi_goal', 'kpi_user_year__user', 'kpi_user_year__kpi_year'
            ).filter(
                kpi_user_year__user__in=userData,
                is_active=True,
                kpi_user_year__kpi_year__current_year=True
            )
            for user in userData:
                goal_appvl = KPILevelPermissions.objects.select_related('kpilevel_id','user_id','assigned_to').filter(user_id = user,action_type=GOAL)
                if goal_appvl:
                    user.is_goal_level = True
                else:
                    user.is_goal_level = False

                # Check if user has any goals (same as detail page logic)
                user_kpi_goals = KpiUserGoal.objects.filter(kpi_user_year__user=user, kpi_user_year__kpi_year__current_year=True)
                user.has_user_goals = user_kpi_goals.exists()
                
                # Check if user has inactive goals (same as detail page logic)
                inactive_goals = KpiUserGoal.objects.filter(
                    kpi_user_year__user=user,
                    kpi_user_year__kpi_year__current_year=True,
                    is_active=False
                )
                user.has_inactive_goals = inactive_goals.exists()
                
                goals_for_user = [goal for goal in user_goals if goal.kpi_user_year.user_id == user.id]
                # user.is_assign_goals = bool(goals_for_user)
                if goals_for_user:
                    max_finish_rate = [val.max_finish_rate for val in goals_for_user if val.max_finish_rate is not None]
                    user.is_goal_val = len(max_finish_rate) == len(goals_for_user)
                else:
                    user.is_goal_val = False
                if kpiuseryearDat:
                    user.period = kpiuseryearDat.id
                
                # commented bcoz of duplicate
                # userGoalData = KpiUserGoal.objects.select_related('kpi_user_year','kpi_goal','kpi_user_year__user','kpi_user_year__kpi_year').filter(kpi_user_year__user=user,is_active=True,kpi_user_year__kpi_year__current_year=True)

                # goal_appvl = KPILevelPermissions.objects.select_related('kpilevel_id','user_id','assigned_to').filter(user_id = user,action_type=GOAL)
                # if goal_appvl:
                #     user.is_goal_level = True
                # else:
                #     user.is_goal_level = False

                # if kpiuseryearDat:
                #     user.period = kpiuseryearDat.id
                # if userGoalData:
                #     user.is_assign_goals = True
                #     max_finish_rate = [val.max_finish_rate for val in userGoalData if val.max_finish_rate != None ]
                #     if len(max_finish_rate) == userGoalData.count():
                #         user.is_goal_val = True
                #     else:
                #         user.is_goal_val = False
                # else:
                #     user.is_assign_goals = False
                #     user.is_goal_val = False

            if is_ajax(request=request):
                context = {}
                userData = User.objects.select_related('profile', 'profile__designation', 'profile__department').filter(id__in=perm_added_user_ids, is_active=True)
                userData = userData.annotate(
                    search=Concat('first_name', Value(' '), 'last_name', Value(' '), 'email', output_field=CharField())
                )

                if search_key:
                    userData = userData.filter(
                        Q(search__icontains=search_key)
                    )

                if department_id:
                    userData = userData.filter(profile__department_id=department_id)

                userData = userData.order_by(Lower("first_name"))

                for user in userData:
                    goal_appvl = KPILevelPermissions.objects.select_related('kpilevel_id','user_id','assigned_to').filter(user_id = user,action_type=GOAL)
                    if goal_appvl:
                        user.is_goal_level = True
                    else:
                        user.is_goal_level = False

                    # Check if user has any goals (same as detail page logic)
                    user_kpi_goals = KpiUserGoal.objects.filter(kpi_user_year__user=user, kpi_user_year__kpi_year__current_year=True)
                    user.has_user_goals = user_kpi_goals.exists()
                    
                    # Check if user has inactive goals (same as detail page logic)
                    inactive_goals = KpiUserGoal.objects.filter(
                        kpi_user_year__user=user,
                        kpi_user_year__kpi_year__current_year=True,
                        is_active=False
                    )
                    user.has_inactive_goals = inactive_goals.exists()
                    
                    goals_for_user = [goal for goal in user_goals if goal.kpi_user_year.user_id == user.id]
                    # user.is_assign_goals = bool(goals_for_user)

                    if goals_for_user:
                        max_finish_rate = [val.max_finish_rate for val in goals_for_user if val.max_finish_rate is not None]
                        user.is_goal_val = len(max_finish_rate) == len(goals_for_user)
                    else:
                        user.is_goal_val = False
                    if kpiuseryearDat:
                        user.period = kpiuseryearDat.id

                data['users'] = userData
                template = loader.render_to_string('hisensehr/kpi/goalset/set_goal_ajax.html', context=data, request=request)
                context['template'] = template
                context['status'] = True
                return JsonResponse(context)
            data['users'] = userData
            data['departments'] = Department.objects.filter(is_active=True)
            return renderfile(request,'kpi/goalset','set_goal_view',data)
        else:
            raise PermissionDenied()
        
class KPIGuidelineView(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpiguideline'
    def get(self,request,*args, **kwargs):
        data = {}  
        kpi_year = str(date.today().year)            
        kpi_year_data = KpiYear.objects.select_related('kpiguideline').all().order_by('start_date')
        data['kpi_year_data'] = kpi_year_data
        data['kpi_year'] = kpi_year
        if is_ajax(request=request):
            action = request.GET.get('action', None)   
            ajax_data = {}
            if action == "load_data":
                context = {'kpi_year_data': kpi_year_data, 'kpi_year':kpi_year}
                ajax_data['template'] = loader.render_to_string('hisensehr/kpi/guideline/general_list.html', context, request=request)

            return JsonResponse(ajax_data)
        return renderfile(request,'kpi/guideline','index',data)

class KPIGuidelineCreateView(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.add_kpiguideline'
    def get(self,request,*args, **kwargs):
        data = {}  
        year = request.GET.get('id', None) 
        if year:
            try:
                kpi_year = KpiYear.objects.select_related('kpiguideline').get(id=year)
                try:
                    form = GuidelineForm(instance=kpi_year.kpiguideline)
                except ObjectDoesNotExist:
                    form = GuidelineForm()
                context = {'kpi_year': kpi_year,'form':form}
                data['status'] = True
                data['template'] = loader.render_to_string('hisensehr/kpi/guideline/general_form.html', context, request=request)
                return JsonResponse(data)
            except ValueError as e:
                print("--e--",e)
                data['status'] = False
                data['message'] = "Something went wrong"
                return JsonResponse(data)
        else:
            data['status'] = False
            data['message'] = "Something went wrong"
            return JsonResponse(data)
        
    def post(self,request,*args, **kwargs):
        data = {}  
        year = request.POST.get('year', None) 
        content = request.POST.get('content', None) 
        if year:
            try:
                kpi_year = KpiYear.objects.get(id=year)
                # obj = KpiGuideline.objects.update_or_create(kpi_year=kpi_year,  defaults={'content': content}) #performance main menu
                obj, created = KpiGuideline.objects.update_or_create(kpi_year=kpi_year, defaults={'content': content})  # performance main menu

                context = {'kpi_year': kpi_year}
                data['status'] = True
                if created:
                    data['message'] = 'Guideline added successfully'
                else:
                    data['message'] = 'Guideline updated successfully'  

                data['template'] = loader.render_to_string('hisensehr/kpi/guideline/general_form.html', context, request=request)

                log_msg = f"KPI guideline edited"
                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_guideline'
                log_data['action_type']  = CREATE
                log_data['log_message']  = log_msg
                log_data['status']  = SUCCESS
                log_data['model_object']  = obj
                log_data['db_data']  = {}
                log_data['app_visibility']  = True
                log_data['web_visibility']  = True
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/kpi-guideline/'  
                LogUserActivity(request, log_data)

                return JsonResponse(data)
            except ValueError as e:
                print("--e--",e)
                data['status'] = False
                data['message'] = "Something went wrong"

                log_msg = f"KPI guideline editing failed"
                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_guideline'
                log_data['action_type']  = CREATE
                log_data['log_message']  = log_msg
                log_data['status']  = FAILED
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = False
                log_data['error_msg']  = e
                log_data['fwd_link']  = '/kpi-guideline/'  
                LogUserActivity(request, log_data)
                return JsonResponse(data)
        else:
            data['status'] = False
            data['message'] = "Something went wrong"

            log_msg = f"KPI guideline editing failed"
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_guideline'
            log_data['action_type']  = CREATE
            log_data['log_message']  = log_msg
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = "Something went wrong"
            log_data['fwd_link']  = '/kpi-guideline/'  
            LogUserActivity(request, log_data)
            
            return JsonResponse(data)

class KpiUserGuidelineView(LoginRequiredMixin,View):
    login_url = '/'
    def get(self,request,*args, **kwargs):
        if KpiActionPermission.objects.filter(assigned_to=request.user,kpi_action__key_action='guideline_settings').exists():
            data = {}
            encrypt = request.GET.get('encrypt', None)
            decrypted = None
            if encrypt:
                try:
                    decrypted = decrypt_me(encrypt)
                    data['decrypted'] = decrypted
                except:
                    raise Http404()
                
            data['time_periods'] = KpiYear.objects.filter(is_active=True)
            perm_added_user_ids = KpiActionPermission.objects.filter(assigned_to=request.user,kpi_action__key_action='guideline_settings').exclude(user_id__isnull=True).values_list('user_id',flat=True).distinct()            
            data['users'] = User.objects.filter(id__in=perm_added_user_ids,is_active=True).order_by(Lower("first_name"))
            return renderfile(request,'kpi/guideline','user_guideline',data)
        else:
            raise PermissionDenied()

class KPIUserGuidelineCreateView(LoginRequiredMixin, View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        if KpiActionPermission.objects.filter(assigned_to=request.user,kpi_action__key_action='guideline_settings').exists():
            data = {}  
            user = request.GET.get('user', None) 
            period = request.GET.get('period', None) 
            if user and period:
                try:
                    user_guideline = KpiUserGuideline.objects.filter(user_id=user,kpi_year_id=period).first()
                    if user_guideline:
                        form = UserGuidelineForm(instance=user_guideline)
                    else:
                        form = UserGuidelineForm()
                    context = {'form':form}
                    data['status'] = True
                    # data['template'] = loader.render_to_string('hisensehr/kpi/guideline/user_form.html', context, request=request)
                    return JsonResponse(data)
                
                except ValueError as e:
                    print("--e--",e)
                    data['status'] = False
                    data['message'] = "Something went wrong"
                    return JsonResponse(data)
            else:
                data['status'] = False
                data['message'] = "Something went wrong"
                return JsonResponse(data)
        else:
            data['status'] = False
            data['message'] = "Permission denied"
            return JsonResponse(data)
        
    def post(self,request,*args, **kwargs):
        data = {}  
        user = request.POST.get('user', None) 
        period = request.POST.get('period', None) 
        content = request.POST.get('content', None) 
        if user:
            try:
                kpi_year = KpiYear.objects.get(id=period)
                obj = KpiUserGuideline.objects.update_or_create(kpi_year=kpi_year, user_id=user,  defaults={'content': content}) #performance main menu
                data['status'] = True
                log_msg = f"User PI guideline edited"
                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_user_guideline'
                log_data['action_type']  = CREATE
                log_data['log_message']  = log_msg
                log_data['status']  = SUCCESS
                log_data['model_object']  = obj
                log_data['db_data']  = {}
                log_data['app_visibility']  = True
                log_data['web_visibility']  = True
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/kpi-user-guideline/create/'  
                LogUserActivity(request, log_data)
                context={}
                user_guideline = KpiUserGuideline.objects.filter(user_id=user,kpi_year_id=period).first()
                if user_guideline:
                    form = UserGuidelineForm(instance=user_guideline)
                else:
                    form = UserGuidelineForm()
                data['status'] = True
                return JsonResponse(data)
            except ValueError as e:
                data['status'] = False
                data['message'] = "Something went wrong"

                log_msg = f"KPI user guideline editing failed"
                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_user_guideline'
                log_data['action_type']  = CREATE
                log_data['log_message']  = log_msg
                log_data['status']  = FAILED
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = False
                log_data['error_msg']  = e
                log_data['fwd_link']  = '/kpi-user-guideline/create/'  
                LogUserActivity(request, log_data)
                return JsonResponse(data)
        else:
            data['status'] = False
            data['message'] = "Something went wrong"

            log_msg = f"KPI user guideline editing failed"
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_user_guideline'
            log_data['action_type']  = CREATE
            log_data['log_message']  = log_msg
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = "Something went wrong"
            log_data['fwd_link']  = '/kpi-user-guideline/create/'  
            LogUserActivity(request, log_data)
            
            return JsonResponse(data)
                
class GoalTemplateView(LoginRequiredMixin,View):
    login_url = '/' 
    def get_kpi_template(self,request,action,user,time_period):
        try:
            context = {}
            user_year = KpiUserYear.objects.filter(user_id=user,kpi_year_id=time_period).last()
            context['user_year'] = user_year
            if action == '1': #assign kpi                
                context['categories'] = KpiCategory.objects.filter(is_active=True).select_related('department').\
                    prefetch_related(Prefetch('kpi_goals',queryset=KpiGoal.objects.filter(is_active=True).select_related('measurement_type').order_by('-id'))).order_by("department","-id")
                if user_year:
                    context['user_goals_ids'] = KpiUserGoal.objects.filter(kpi_user_year_id=user_year.id,is_active=True).values_list('kpi_goal_id',flat=True)
                template = loader.render_to_string('hisensehr/kpi/goalset/assign_goal.html',context=context,request=request)
            elif action == '2': #set goal
                if user_year:
                    context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                        filter(kpi_user_year_id=user_year.id,is_active=True).\
                            values('id','kpi_goal__kpi_category__name','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                template = loader.render_to_string('hisensehr/kpi/goalset/set_goal.html',context=context,request=request)
            elif action == '3': #set permission
                if user_year:
                    # context['user_permissions'] = KpiActionPermission.objects.filter(kpi_action__kpi_action_type=2).select_related('user','kpi_action','assigned_to').annotate(
                    #     full_name=Concat('user__first_name',
                    #         Value(' '),
                    #         'user__last_name'
                    #         ,output_field=CharField()))\
                    #     .values('id','full_name','user_id','assigned_to__first_name','assigned_to__last_name','assigned_to_id','kpi_action_id').order_by('-id')
                    context['users'] = User.objects.filter(is_active=True).exclude(is_superuser=True).order_by(Lower('first_name')).annotate(
                        full_name=Concat('first_name', Value(' '),'last_name',output_field=CharField())).prefetch_related(Prefetch('user_kpi_permissions',queryset= KpiActionPermission.objects.filter(kpi_action__kpi_action_type=2)))
                    context['actions'] = KpiAction.objects.filter(is_active=True,kpi_action_type=2).order_by('id')[2:4]
                    context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                                                prefetch_related('at_goal_permissions').\
                                                filter(kpi_user_year_id=user_year.id,is_active=True).\
                                                only('id','kpi_goal__kpi_category__name','kpi_goal__measurement_type__name','kpi_goal__name').order_by('-kpi_goal__kpi_category__id','id')
                template = loader.render_to_string('hisensehr/kpi/goalset/set_goal_permission.html',context=context,request=request)
            else:
                template = None
            return template
        except Exception as e:
            print(".......",e)
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_goal_template'
            log_data['action_type']  = READ
            log_data['log_message']  = ''
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/kpi-goal-template/'
            LogUserActivity(request, log_data)
            return None

    def get(self,request,*args, **kwargs):
        if KpiActionPermission.objects.filter(assigned_to=request.user,kpi_action__key_action='goal_settings').exists():
            data = {}
            action = request.GET.get('action')
            user = request.GET.get('user')
            period = request.GET.get('period')
            template = self.get_kpi_template(request=request,action=action,user=user,time_period=period)
            if KpiActionPermission.objects.filter(assigned_to=request.user,user_id=user,kpi_action__key_action='goal_settings').exists():
                if template:
                    data['status'] = True
                    data['template'] = template
                else:
                    data['status'] = False
                    data['message'] = "Something went wrong"
            else:
                data['status'] = False
                data['message'] = "You have no permission to do this action"
            return JsonResponse(data)
        else:
            raise PermissionDenied()

class GoalTemplateAction(LoginRequiredMixin,View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        if KpiActionPermission.objects.filter(assigned_to=request.user,kpi_action__key_action='goal_settings').exists():
            data = {}
            period = kwargs['period'] 
            action = kwargs['action'] 
            userid = kwargs['userid'] 

            context = {}
            context['userid'] = userid
            context['action'] = action
            context['period'] = period
            user_year = KpiUserYear.objects.select_related('user','user__profile').filter(user_id=userid,kpi_year_id=period).last()
            user_probation = Profile.objects.select_related('user','user__profile').get(user=userid)
            if action == 1: #assign kpi 
                result = calculate_probation(user_probation.user,user_probation.date_of_joining)
                if result:
                    context['probation_completed'] = True    
                    if is_ajax(request=request):
                        search_key = request.GET.get('search_key', None)
                        department_id = request.GET.get('department', None)
                        categories = KpiCategory.objects.filter(department_id=user_probation.department.id) #is_active=True
                        if department_id:
                            categories = categories.filter(department=department_id).select_related('department')
                        if search_key:
                            categories = categories.filter(Q(name__icontains=search_key))
                        categories = categories.prefetch_related(
                            Prefetch(
                                'kpi_goals',
                                queryset=KpiGoal.objects.all().annotate( #filter(is_active=True)
                                    has_user_goal=Exists(
                                        KpiUserGoal.objects.filter(kpi_goal=OuterRef('id'), kpi_user_year=user_year)
                                    )
                                ).filter(has_user_goal=True).select_related('measurement_type').annotate(
                                    user_goal_id=F('kpiusergoal__id'),
                                    goal_value=F('kpiusergoal__goal_value'),
                                    max_finish_rate=F('kpiusergoal__max_finish_rate'),
                                    weight=F('kpiusergoal__weight')
                                ).order_by('user_goal_id')
                            )
                        ).order_by("department", "id")

                        data_exist_flag = False
                        for category in categories:
                            if len(category.kpi_goals.all()) > 0:
                                data_exist_flag = True
                                break
                        
                        context['categories'] = categories
                        context['data_exist_flag'] = data_exist_flag
                        # to check number of goals
                        categories = categories.annotate(
                            active_goals_count=Count('kpi_goals', filter=Q(kpi_goals__is_active=True))
                        ).filter(active_goals_count__gt=0)
                        len_data = len(categories)
                        context['len_data'] = len_data                    
                    else: 
                        context['categories'] = KpiCategory.objects.filter(
                            department_id=user_probation.department.id).select_related('department').prefetch_related( #is_active=True
                            Prefetch(
                                'kpi_goals',
                                queryset=KpiGoal.objects.all().annotate( #filter(is_active=True) # to fix the issue of not showing inactive goals
                                    has_user_goal=Exists(
                                        KpiUserGoal.objects.filter(
                                            kpi_goal=OuterRef('id'),
                                            kpi_user_year=user_year
                                        )
                                    )
                                ).filter(has_user_goal=True).select_related('measurement_type').annotate(
                                    user_goal_id=Subquery(
                                        KpiUserGoal.objects.filter(
                                            kpi_goal=OuterRef('id'),
                                            kpi_user_year=user_year
                                        ).values('id')[:1]
                                    ),
                                    goal_value=Subquery(
                                        KpiUserGoal.objects.filter(
                                            kpi_goal=OuterRef('id'),
                                            kpi_user_year=user_year
                                        ).values('goal_value')[:1]
                                    ),
                                    max_finish_rate=Subquery(
                                        KpiUserGoal.objects.filter(
                                            kpi_goal=OuterRef('id'),
                                            kpi_user_year=user_year
                                        ).values('max_finish_rate')[:1]
                                    ),
                                    weight=Subquery(
                                        KpiUserGoal.objects.filter(
                                            kpi_goal=OuterRef('id'),
                                            kpi_user_year=user_year
                                        ).values('weight')[:1]
                                    ),
                                    user_goal_is_active=Subquery(
                                        KpiUserGoal.objects.filter(
                                            kpi_goal=OuterRef('id'),
                                            kpi_user_year=user_year
                                        ).values('is_active')[:1]
                                    )
                                ).order_by('kpi_category_id', 'user_goal_id')
                            )
                        ).order_by("department", "id")
                        len_data = len(context['categories'])
                        context['len_data'] = len_data

                        context['total_weight'] = 0
                    if user_year:
                        user_goals = KpiUserGoal.objects.select_related('kpi_user_year').filter(kpi_user_year_id=user_year.id).values('id', 'kpi_goal_id','goal_value','max_finish_rate','weight', 'is_active').order_by('id')
                      
                        # Debug: Print the categories and their annotations
                        # Order categories based on the sequence of user goal IDs
                        categories_with_user_goals = []
                        seen_category_names = set()
                        category_first_goal_id = {}  # Track the first goal ID for each category

                        # Debug: Print available keys in user_goals
                        
                        # First pass: collect categories and their first appearing goal ID
                        # Create ordered categories based on user_goals sequence
                        ordered_categories = []
                        seen_categories = set()

                        # First, create a mapping of goal IDs to categories
                        goal_id_to_category = {}
                        for category in context['categories']:
                            for goal in category.kpi_goals.all():
                                user_goal_id = getattr(goal, 'user_goal_id', None)
                                if user_goal_id:
                                    goal_id_to_category[user_goal_id] = category

                        print(f"DEBUG: Goal ID to category mapping: {[(k, v.name) for k, v in goal_id_to_category.items()]}")

                        # Now order categories based on user_goals sequence
                        for user_goal in user_goals:
                            user_goal_id = user_goal['id']
                            print(f"DEBUG: Processing user_goal ID: {user_goal_id}")

                            # Find the category for this user goal
                            category = goal_id_to_category.get(user_goal_id)
                            if category and category not in seen_categories:
                                ordered_categories.append(category)
                                seen_categories.add(category)
                                print(f"DEBUG: Added category '{category.name}' for user_goal ID {user_goal_id}")

                        # Update context with ordered categories
                        context['categories'] = ordered_categories
                        print(f"DEBUG: Final ordered categories: {[cat.name for cat in ordered_categories]}")
                                    # Now you can compare or use user_goal_id as needed
                            

                        active_user_goals = user_goals.filter(is_active=True)
                        context['user_goals'] = active_user_goals
                        context['total_weight'] = sum(item['weight'] for item in user_goals)

                        context['user_goal_ids'] = [user_goal['kpi_goal_id'] for user_goal in active_user_goals]

                else:
                    context['probation_completed'] = False   
                context['selected_user'] = user_probation.user
                context['departments'] = Department.objects.filter(is_active=True).order_by('name')
                context['measurement_types'] = MeasurementType.objects.filter(is_active=True)
                context['kpi_user_goal'] = KpiUserGoal.objects.filter(kpi_user_year=user_year)
                context['has_user_goals'] = context['kpi_user_goal'].exists()
                user_goals = KpiUserGoal.objects.filter(
                    kpi_user_year__user=userid,kpi_user_year__kpi_year__current_year=True,
                    is_active=False  # Only check for inactive goals
                )
                # Set the context variable
                context['has_inactive_goals'] = user_goals.exists()
                context['department_goals'] = KpiGoal.objects.filter(is_active=True, kpi_category__department=user_probation.department)
                context['current_year'] = KpiYear.objects.filter(current_year=True).last().name
                if is_ajax(request=request):
                    template = loader.render_to_string('hisensehr/kpi/goalset/asign_goal_ajax.html', context=context, request=request)
                    data['template'] = template
                    data['status'] = True
                    data['len_data'] = len_data
                    return JsonResponse(data)
                else:
                    return renderfile(request, 'kpi/goalset', 'assign_kpi_goal_values', context)
                    # return renderfile(request, 'kpi/goalset', 'asign_goal', context)
            
            elif action == 2: #set goal
                if user_year:
                    context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                        filter(kpi_user_year_id=user_year.id,is_active=True).\
                            values('id','kpi_goal__kpi_category__name','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                    context['total_weight'] = context['user_goals'].aggregate(sum_weight=Sum('weight'))['sum_weight']
                # template = loader.render_to_string('hisensehr/kpi/goalset/set_goal.html',context=context,request=request)

                return renderfile(request, 'kpi/goalset', 'set_goal_value', context)
        
            elif action == 3:
                user_guideline = KpiUserGuideline.objects.filter(user_id=userid,kpi_year_id=period).first()
                if user_guideline:
                    form = UserGuidelineForm(instance=user_guideline)
                else:
                    form = UserGuidelineForm()
                context = {'form':form}
                context['status'] = True
                context['userid'] = userid
                context['action'] = action
                context['period'] = period
                context['current_year'] = KpiYear.objects.filter(current_year=True,is_active=True).last()
                # data['template'] = loader.render_to_string('hisensehr/kpi/guideline/user_form.html', context, request=request)
                return renderfile(request, 'kpi/goalset', 'user_guideline', context)

            # return HttpResponse("Invalid action", status=404)

class AssignKpiView(LoginRequiredMixin,View):
    login_url = '/'
    def post(self,request,*args, **kwargs):
        data = {}
        context = {}
        try:
            with transaction.atomic():
                action = request.POST.get('action')
                user = request.POST.get('user')
                period = request.POST.get('period')
                goal_data = request.POST.get('goal_data')
                goal_data_list = json.loads(goal_data)
                
                total_weight = 0
                for item in goal_data_list:
                    total_weight += float(item['weight'])
                if KpiUserYear.objects.select_related('kpi_year','user').filter(kpi_year_id=period,user_id=user,status=2).exists():
                    data['status'] = False
                    data['message'] = "Goals are either active or approved and cannot be edited at this stage."
                else:
                    if len(goal_data_list) > 0:
                        if total_weight == 100:
                            if KpiActionPermission.objects.select_related('assigned_to','user','kpi_action').filter(assigned_to=request.user,user_id=user,kpi_action__key_action='goal_settings').exists():
                                user_year = KpiUserYear.objects.update_or_create(kpi_year_id=period,user_id=user,defaults={'status':1})#Pending status
                                # nov 22
                                KpiUserGoal.objects.filter(kpi_user_year_id=user_year[0].id).update(is_active=False)
                                edit_flag = 0
                                for goal_vals in goal_data_list:
                                    user_kpi_goals = KpiUserGoal.objects.select_related('kpi_user_year','kpi_goal').filter(kpi_user_year_id=user_year[0].id,kpi_goal_id=float(goal_vals['id']))
                                    if user_kpi_goals:
                                        user_kpi_goals.update(kpi_goal_id=float(goal_vals['id']),goal_value = goal_vals['goal_value'],max_finish_rate = float(goal_vals['max_finish_rate']),weight = float(goal_vals['weight']),is_active=True)
                                        edit_flag = 1
                                    else:
                                        KpiUserGoal.objects.create(kpi_user_year_id=user_year[0].id,kpi_goal_id=float(goal_vals['id']),goal_value = goal_vals['goal_value'],max_finish_rate = float(goal_vals['max_finish_rate']),weight = float(goal_vals['weight']),added_by = request.user,is_active=True)
                                    # user_kpi_goals = KpiUserGoal.objects.update_or_create(kpi_user_year_id=user_year[0].id,kpi_goal_id=float(goal_vals['id']),goal_value = int(goal_vals['goal_value']),max_finish_rate = float(goal_vals['max_finish_rate']),weight = float(goal_vals['weight']),defaults={'added_by':request.user,'is_active':True})                            
                                
                                # saving to kpi approve table
                                kpi_assignee = KPILevelPermissions.objects.select_related('user_id').filter(user_id_id = user,is_active=True,action_type=GOAL)
                                kpi_approval = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpiuser_timeperiod').filter(action_type=GOAL,kpi_user_year__kpi_year_id=period,approved_rejected_user_id = user)
                                missing_approvals = kpi_assignee.exclude(Exists(kpi_approval.filter(added_by=OuterRef('assigned_to'),kpilevel_id=OuterRef('kpilevel_id'))) )
                                new_approvals = [
                                    KPIApprovalStatus(
                                        approved_rejected_user_id=user,
                                        added_by=kpi_usr.assigned_to,
                                        kpilevel_id=kpi_usr.kpilevel_id,
                                        is_approve=False,
                                        is_active=False,
                                        kpi_user_year=user_year[0],
                                        action_type=GOAL
                                    )
                                    for kpi_usr in missing_approvals
                                ]
                                if new_approvals:
                                    KPIApprovalStatus.objects.bulk_create(new_approvals)

                                # if not kpi_approval:
                                #     for kpi_usr in kpi_assignee:
                                #         kpi_aprv_data = KPIApprovalStatus.objects.create(approved_rejected_user_id = user, added_by = kpi_usr.assigned_to, kpilevel_id = kpi_usr.kpilevel_id, is_approve=False, is_active=False,kpi_user_year=user_year[0],action_type=GOAL)

                                # saving to kpi approve table
                                current_year = KpiYear.objects.get(current_year=True)
                                kpi_approval = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpiuser_timeperiod').filter(action_type=GOAL,kpi_user_year__kpi_year=current_year,approved_rejected_user_id = user)
                                if not kpi_approval:
                                    kpi_assignee = KPILevelPermissions.objects.select_related('user_id').filter(user_id_id = user,is_active=True,action_type=GOAL)
                                    for kpi_usr in kpi_assignee:
                                       KPIApprovalStatus.objects.create(approved_rejected_user_id = user, added_by = kpi_usr.assigned_to, kpilevel_id = kpi_usr.kpilevel_id, is_approve=False, is_active=False,kpi_user_year=user_year[0],action_type=GOAL)
                                else:
                                    kpi_approval.update(is_approve=False, is_active=False,approval_status=PENDING)

                                # nov 22 update
                                this_user = User.objects.only('first_name','last_name').get(id=user)

                                #notification
                                scheme = request.is_secure() and "https://" or "http://"
                                current_site = get_current_site(request)
                                domainlink=scheme+current_site.domain  

                                link = domainlink+'/goal-approve-detail-view/'+encrypt_me(user)+'/'+encrypt_me(user_year[0].kpi_year.id) 
                                # if edit_flag == 1:
                                #     msg = f"{this_user.first_name} {this_user.last_name}'s goal values has been updated. Please restart goal approval"
                                # else:
                                msg = f"{this_user.first_name} {this_user.last_name}'s goal values has been updated. Please start goal approval"
  
                                assignedto = request.user
                                info = {}
                                info['type'] = 'kpi_goal_permission_kpi'
                                info['action'] = 'Create'
                                info['action_id'] = int(action)
                                info['url'] = link
                                info['end_user'] = this_user.id
                                notificationUpdate(user_from=request.user,
                                                                    user_to=assignedto,
                                                                    message=msg, info=info)


                                #email notification
                                mail_subject = 'Update KPI Values'                    
                                user_name = assignedto.first_name+" "+assignedto.last_name
                                content_replace = {"NAME":this_user.first_name+' '+this_user.last_name, "MESSAGE":msg, "LINK":link, "TITLE":"Update KPI values","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, assignedto.email)

                                #log entry
                                log_data = {}
                            
                                log_data['module_name']  = 'kpi_goal_assigned'
                                log_data['action_type']  = CREATE
                                log_data['log_message']  = f'Assigned KPI goals to {this_user.first_name} {this_user.last_name}'
                                log_data['status']  = SUCCESS
                                log_data['model_object']  = user_year[0]
                                log_data['db_data']  = {}
                                log_data['app_visibility']  = True
                                log_data['web_visibility']  = True
                                log_data['error_msg']  = ''
                                log_data['fwd_link']  = '/kpi-assign-goal/'
                                LogUserActivity(request, log_data)

                                context['categories'] = KpiCategory.objects.filter(is_active=True).select_related('department').\
                                    prefetch_related(Prefetch('kpi_goals',queryset=KpiGoal.objects.filter(is_active=True).select_related('measurement_type').order_by('-id'))).order_by("department","-id")
                                context['user_goals_ids'] = KpiUserGoal.objects.filter(kpi_user_year_id=user_year[0].id,is_active=True).values_list('kpi_goal_id',flat=True)
                                # template = loader.render_to_string('hisensehr/kpi/goalset/assign_goal.html',context=context,request=request)
                                # data['template'] = template
                                data['status'] = True
                                data['edit_flag'] = edit_flag
                                return JsonResponse(data)
                                # return renderfile(request, 'kpi/goalset', 'asign_goal', context)

                            else:
                                data['status'] = False
                                data['message'] = "You have no permission to do this action"
                        else:
                            data['status'] = False
                            data['message'] = "Total weight should be 100%"
                    else:
                        data['status'] = False
                        data['message'] = "Total weight should be 100%"
        except Exception as e:
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_assign_goal'
            log_data['action_type']  = CREATE
            log_data['log_message']  = 'KPI goals assignment failed'
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/kpi-assign-goal/'
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = "Somthing went wrong"
            print("....",e)
        return JsonResponse(data)

class SetGoalView(LoginRequiredMixin,View):
    login_url = '/'
    def post(self,request,*args, **kwargs):
        data = {}
        context = {}
        try:
            with transaction.atomic():
                action = request.POST.get('action')
                user = request.POST.get('user')
                period = request.POST.get('period')
                data_set = request.POST.get('data_set')
                data_set = json.loads(data_set)
                # user_time_period = KpiUserTimePeriod.objects.select_related('user','kpi_time_period').filter(user_id=user,kpi_time_period_id=period).last()
                user_year = KpiUserYear.objects.select_related('user','user__profile','kpi_year').filter(user_id=user,kpi_year_id=period).last()
                if user_year.status > 1:
                    data['status'] = False
                    data['message'] = "Goals are either active or approved and cannot be edited at this stage."
                else:
                    if KpiActionPermission.objects.filter(assigned_to=request.user,user_id=user,kpi_action__key_action='goal_settings').exists():
                        goal_data = data_set['goal_data']
                        max_rate_data = data_set['max_rate_data'] 
                        weight_data = data_set['weight_data']
                        total_weight = 0
                        for item in weight_data:
                            total_weight += float(item['value'])
                        if total_weight == 100:
                            for item in goal_data:
                                KpiUserGoal.objects.filter(id=item['id']).update(goal_value=item['value'])
                            for item in max_rate_data:
                                if float(item['value']) <= 1:
                                    raise ValidationError("Minimum value for maximum finish rate is 1")
                                KpiUserGoal.objects.filter(id=item['id']).update(max_finish_rate=item['value'])
                            for item in weight_data:
                                if float(item['value']) < 0:
                                    raise ValidationError("Minimum value for weight is 0")
                                KpiUserGoal.objects.filter(id=item['id']).update(weight=item['value'])
                            
                            #log entry
                            log_data = {}
                            log_data['module_name']  = 'kpi_goal_value'
                            log_data['action_type']  = CREATE
                            log_data['log_message']  = f"KPI goal values of {user_year.user.first_name} {user_year.user.last_name} are done"
                            log_data['status']  = SUCCESS
                            log_data['model_object']  = None
                            log_data['db_data']  = {}
                            log_data['app_visibility']  = True
                            log_data['web_visibility']  = True
                            log_data['error_msg']  = ''
                            log_data['fwd_link']  = '/kpi-goal-settings/'
                            LogUserActivity(request, log_data)

                            kpi_user_year = KpiUserYear.objects.filter(user_id=user,kpi_year_id=period).last()
                            KpiUserYear.objects.filter(user_id=user,kpi_year_id=period).update(status=1) #goal setted status
                            context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                                    filter(kpi_user_year_id=user_year.id,is_active=True).\
                                    values('id','kpi_goal__kpi_category__name','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')

                            #notification
                            scheme = request.is_secure() and "https://" or "http://"
                            current_site = get_current_site(request)
                            domainlink=scheme+current_site.domain                
                            link = domainlink+'/kpi-approve-goal/?encrypt='+encrypt_me(user_year.user_id)

                            assigned_users = HeadOfDepartment.objects.select_related('user').filter(department_id=user_year.user.profile.department_id)
                            msg = f"{request.user.first_name} {request.user.last_name} has created KPI yearly goals of {user_year.user.first_name} {user_year.user.last_name} and waiting for your activation"
                            info = {}
                            info['type'] = 'kpi_yearly_goal_created'
                            info['action'] = 'Create'
                            info['action_id'] = kpi_user_year.id
                            info['user_id'] = user
                            info['year_id'] = period
                            info['url'] = link
                            info['end_user'] = user_year.user_id
                            for assigned_user in assigned_users:

                                notificationUpdate(user_from=request.user,
                                                                    user_to=assigned_user.user,
                                                                    message=msg, info=info)
                                #email notification
                                mail_subject = 'KPI Yearly Goals'                                
                                user_name = assigned_user.user.first_name+" "+assigned_user.user.last_name
                                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI yearly goals - created","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, assigned_user.user.email)
                            
                            # template = loader.render_to_string('hisensehr/kpi/goalset/set_goal.html',context=context,request=request)
                            # data['template'] = template
                            data['status'] = True
                            return JsonResponse(data)
                            # return renderfile(request, 'kpi/goalset', 'set_goal_value', context)
                        

                        else:
                            data['status'] = False
                            data['message'] = "Total weight should be 100%"
                    else:
                        data['status'] = False
                        data['message'] = "You have no permission to do this action"
        except ValidationError as e:
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_goal_settings'
            log_data['action_type']  = CREATE
            log_data['log_message']  = f"Goal values setting failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/kpi-goal-settings/'
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = e.message
        except Exception as e:
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_goal_settings'
            log_data['action_type']  = CREATE
            log_data['log_message']  = f"Goal values setting failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/kpi-goal-settings/'
            LogUserActivity(request, log_data)
            print(".....",e)
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class MyKpiGoalView(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpiusertarget' 
    def get(self,request,*args, **kwargs):
        data = {}
        data['time_periods'] = KpiYear.objects.filter(is_active=True)
        return renderfile(request,'kpi/mykpi','goal_view',data)       

    
class GoalApprovalView(LoginRequiredMixin,View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        if HeadOfDepartment.objects.filter(user=request.user,is_active=True).exists() or request.user.is_superuser:
            data = {}
            encrypt = request.GET.get('encrypt', None)
            decrypted = None
            if encrypt:
                try:
                    decrypted = decrypt_me(encrypt)
                    data['decrypted'] = decrypted
                except:
                    raise Http404()
            department_ids = HeadOfDepartment.objects.filter(is_active=True,user=request.user).values_list('department_id',flat=True)
            if request.user.is_superuser:
                data['users'] = User.objects.exclude(is_superuser=True).filter(is_active=True).\
                    order_by(Lower('first_name'))
            else:
                data['users'] = User.objects.exclude(is_superuser=True).filter(is_active=True,profile__department_id__in=department_ids).\
                    order_by(Lower('first_name'))
            data['time_periods'] = KpiYear.objects.filter(is_active=True)
            return renderfile(request,'kpi/goalapprove','goal_approval_view',data)
        else:
            raise PermissionDenied()

    def post(self,request,*args, **kwargs):
        if HeadOfDepartment.objects.filter(user=request.user,is_active=True).exists() or request.user.is_superuser:
            data = {}
            context = {}
            try:
                with transaction.atomic():
                    data_set = request.POST.get('data_set')
                    data_set = json.loads(data_set)
                    user_year = request.POST.get('user_time_period')
                    radio = request.POST.get('radio')
                    total_weight = 0
                    goal_data = data_set['goal_data']
                    max_rate_data = data_set['max_rate_data']
                    weight_data = data_set['weight_data']
                    total_weight = 0
                    for item in weight_data:
                        total_weight += float(item['value'])
                    if total_weight == 100:
                        data['status'] = True
                        for item in goal_data:
                            KpiUserGoal.objects.filter(id=item['id']).update(goal_value=item['value'])
                        for item in max_rate_data:
                            if float(item['value']) <= 1:
                                raise ValidationError("Minimum value for maximum finish rate is 1")
                            KpiUserGoal.objects.filter(id=item['id']).update(max_finish_rate=item['value'])
                        for item in weight_data:
                            if float(item['value']) < 0:
                                raise ValidationError("Minimum value for weight is 0")
                            KpiUserGoal.objects.filter(id=item['id']).update(weight=item['value'])
                        
                        scheme = request.is_secure() and "https://" or "http://"
                        current_site = get_current_site(request)
                        domainlink=scheme+current_site.domain 
                        
                        if  radio == 'active':
                            with transaction.atomic():
                                kpi_user_year = KpiUserYear.objects.select_related('user').get(id=user_year)
                                KpiUserYear.objects.filter(id=user_year).update(status=2)#goal pending
                                
                                #notification                                              
                                link = domainlink+'/my-kpi-goals/'

                                msg = f"{request.user.first_name} {request.user.last_name} has activated your kpi yearly goals, please approve your goals"
                                info = {}
                                info['type'] = 'kpi_yearly_goal_activated'
                                info['action'] = 'Create'
                                info['action_id'] = kpi_user_year.id
                                info['url'] = link
                                notificationUpdate(user_from=request.user,
                                                                    user_to=kpi_user_year.user,
                                                                    message=msg, info=info)
                                #email notification
                                mail_subject = 'KPI Yearly Goals'                                
                                user_name = kpi_user_year.user.first_name+" "+kpi_user_year.user.last_name
                                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI yearly goals activated","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, kpi_user_year.user.email)

                                log_msg = f"Activated KPI goals of {kpi_user_year.user.first_name} {kpi_user_year.user.last_name}"
                                #log entry                      
                                log_data = {}
                                log_data['module_name']  = 'kpi_yearly_goal_activated'
                                log_data['action_type']  = CREATE
                                log_data['log_message']  = log_msg
                                log_data['status']  = SUCCESS
                                log_data['model_object']  = kpi_user_year
                                log_data['db_data']  = {}
                                log_data['app_visibility']  = True
                                log_data['web_visibility']  = True
                                log_data['error_msg']  = ''
                                log_data['fwd_link']  = '/kpi-approve-goal/?encrypt='+encrypt_me(kpi_user_year.user_id)
                                LogUserActivity(request, log_data)

                                data['action'] = "Goal status changed to active successfully"
                        elif radio == 'reject':
                            KpiUserYear.objects.filter(id=user_year).update(status=3,rejected_by=request.user,rejected_at=datetime.now())#goal rejected
                            data['action'] = "Goal rejected successfully"
                        elif radio == 'pending':
                            #goal pending
                            kpiuser = KpiUserYear.objects.select_related('user').get(id=user_year)
                            kpiuser.status=1
                            kpiuser.approved_by=None
                            kpiuser.approved_at=None
                            kpiuser.rejected_by=None
                            kpiuser.rejected_at=None
                            kpiuser.employee_signature = None
                            kpiuser.employee_signature_date = None
                            kpiuser.hod_signature = None
                            kpiuser.hod_signature_date = None
                            kpiuser.save()
                            
                            log_msg = f"Changed KPI goals of {kpiuser.user.first_name} {kpiuser.user.last_name} to pending"
                            #log entry                      
                            log_data = {}
                            log_data['module_name']  = 'kpi_yearly_goal_activated'
                            log_data['action_type']  = CREATE
                            log_data['log_message']  = log_msg
                            log_data['status']  = SUCCESS
                            log_data['model_object']  = kpiuser
                            log_data['db_data']  = {}
                            log_data['app_visibility']  = True
                            log_data['web_visibility']  = True
                            log_data['error_msg']  = ''
                            log_data['fwd_link']  = '/kpi-approve-goal/?encrypt='+encrypt_me(kpiuser.user_id)
                            LogUserActivity(request, log_data)
                            
                            data['action'] = "Goal status changed to pending successfully"
                    else:
                        data['status'] = False
                        data['message'] = "Total weight should be 100%"

                    context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                                    filter(kpi_user_year_id=user_year,is_active=True).\
                                    values('id','kpi_goal__kpi_category__name','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').\
                                        order_by('-kpi_goal__kpi_category__id','id')
                    context['user_year'] = KpiUserYear.objects.select_related('user').prefetch_related('user_year_kpi_values').filter(id=user_year).last()
                    template = loader.render_to_string("hisensehr/kpi/goalapprove/goals_approval_ajax.html",request=request,context=context)
                    data['template'] = template

            except ValidationError as e:
                data['status'] = False
                data['message'] = e.message
                #log entry
                log_data = {}
                log_data['module_name']  = 'kpi_yearly_goal_activated'
                log_data['action_type']  = CREATE
                log_data['log_message']  = ''
                log_data['status']  = FAILED
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = False
                log_data['error_msg']  = e.message
                log_data['fwd_link']  = ''
                LogUserActivity(request, log_data)


            except Exception as e:
                print(".....",e)
                #log entry
                log_data = {}
                log_data['module_name']  = 'kpi_yearly_goal_activated'
                log_data['action_type']  = CREATE
                log_data['log_message']  = ''
                log_data['status']  = FAILED
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = False
                log_data['error_msg']  = e
                log_data['fwd_link']  = ''
                LogUserActivity(request, log_data)

                data['status'] = False
                data['message'] = "something went wrong"
            return JsonResponse(data)
        else:
            raise PermissionDenied()

class LoadMyGoalsView(LoginRequiredMixin,View):
    login_url = '/'
    def get(self,request,*args, **kwargs):
        data = {}
        context = {}
        try:
            user = request.user.pk
            period = request.GET.get('time_period')

            user_year = KpiUserYear.objects.select_related('kpi_year').filter(user_id=user,kpi_year_id=period).last()
            if user_year:
                user_goals = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                            filter(kpi_user_year_id=user_year.id,is_active=True).\
                            values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').\
                                order_by('-kpi_goal__kpi_category__id','id')

                context['total_weight'] = sum(item['weight'] for item in user_goals)
                context['user_goals'] = user_goals
                context['user_year'] = user_year
            template = loader.render_to_string("hisensehr/kpi/mykpi/goals_view_ajax.html",request=request,context=context)
            data['template'] = template
            data['status'] = True
        except Exception as e:
            print("......",e)
            #log entry
            log_data = {}
            log_data['module_name']  = 'load_goal_view'
            log_data['action_type']  = READ
            log_data['log_message']  = ''
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/my-kpi-load-goal/'
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = "something went wrong"
        return JsonResponse(data)

class LoadGoalsView(LoginRequiredMixin,View):
    login_url = '/'
    def get(self,request,*args, **kwargs):
        if HeadOfDepartment.objects.filter(user=request.user,is_active=True).exists() or request.user.is_superuser:
            data = {}
            context = {}
            try:
                user = request.GET.get('user')
                period = request.GET.get('time_period')
                user_year = KpiUserYear.objects.select_related('user').prefetch_related('user_year_kpi_values').filter(user_id=user,kpi_year_id=period).last()
                if user_year:
                    context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                                filter(kpi_user_year_id=user_year.id,is_active=True).\
                                values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').\
                                    order_by('-kpi_goal__kpi_category__id','id')
                    context['user_year'] = user_year
                template = loader.render_to_string("hisensehr/kpi/goalapprove/goals_approval_ajax.html",request=request,context=context)
                data['template'] = template
                data['status'] = True
            except Exception as e:
                print("......",e)
                #log entry
                log_data = {}
                log_data['module_name']  = 'kpi_approve_load_goal'
                log_data['action_type']  = READ
                log_data['log_message']  = ''
                log_data['status']  = FAILED
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = False
                log_data['error_msg']  = e
                log_data['fwd_link']  = '/kpi-approve-load-goal/'
                LogUserActivity(request, log_data)

                data['status'] = False
                data['message'] = "something went wrong"
            return JsonResponse(data)
        else:
            raise PermissionDenied()

class KpiGoalSelfApprove(LoginRequiredMixin, View):
    login_url = '/'
    def post(self,request,*args, **kwargs):
        data = {}
        user_time_period = request.POST.get("user_time_period")
        goal_employee_signature = request.FILES.get("goal_employee_signature")
        employee_signature_date = datetime.today().date()
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain     
        if goal_employee_signature:
            extension = goal_employee_signature.name.split('.')[-1].upper()
            if not extension in ['JPEG','PNG', 'JPG']:
                data['status'] = False
                data['message'] = "Invalid signature file type, allowed extentions are JPEG, PNG, JPG"
                return JsonResponse(data)
        try:
            with transaction.atomic():
                user_year = KpiUserYear.objects.get(id=user_time_period)
                user_year.status = 3 #imployee approved
                user_year.employee_signature = goal_employee_signature
                user_year.employee_signature_date = employee_signature_date
                user_year.save()

                #notification                           
                link = domainlink+'/kpi-approve-goal/?encrypt='+encrypt_me(request.user.pk)

                assigned_users = HeadOfDepartment.objects.select_related('user').filter(department_id=request.user.profile.department_id)
                msg = f"Kpi yearly goals has been approved by {request.user.first_name} {request.user.last_name}, waiting for your approval"
                info = {}
                info['type'] = 'kpi_yearly_goal_emp_approved'
                info['action'] = 'Create'
                info['action_id'] = user_year.id
                info['url'] = link
                info['end_user'] = request.user.pk

                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_yearly_goal_emp_approved'
                log_data['action_type']  = CREATE
                log_data['log_message']  = "Approved kpi yearly goals"
                log_data['status']  = SUCCESS
                log_data['model_object']  = user_year
                log_data['db_data']  = {}
                log_data['app_visibility']  = True
                log_data['web_visibility']  = True
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/my-kpi-goals/'
                LogUserActivity(request, log_data)
                
                for assigned_user in assigned_users:
                    notificationUpdate(user_from=request.user,
                                                        user_to=assigned_user.user,
                                                        message=msg, info=info)
                    #email notification
                    mail_subject = 'KPI Yearly Goals'                    
                    user_name = assigned_user.user.first_name+" "+assigned_user.user.last_name
                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI yearly goals - Employee Approved","YEAR": datetime_timezone.now().year}
                    send_email_to_users.delay(mail_subject, 4, content_replace, assigned_user.user.email)
            data['status'] = True
        except Exception as e:
            print("......",e)
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'my_kpi_goals'
            log_data['action_type']  = CREATE
            log_data['log_message']  = "Kpi yearly goals approval failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/my-kpi-goals/'
            LogUserActivity(request, log_data)
            
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class KpiGoalHodApprove(LoginRequiredMixin, View):
    login_url = '/'
    def post(self,request,*args, **kwargs):
        data = {}
        user_time_period = request.POST.get("user_time_period")
        kpi_goal_hod_signature = request.FILES.get("kpi_goal_hod_signature")
        hod_signature_date = datetime.today().date()
        data_set = request.POST.get('data_set')
        data_set = json.loads(data_set)
        context = {}
        radio = 'approve'
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  
        if kpi_goal_hod_signature:
            extension = kpi_goal_hod_signature.name.split('.')[-1].upper()
            if not extension in ['JPEG','PNG', 'JPG']:
                data['status'] = False
                data['message'] = "Invalid signature file type, allowed extentions are JPEG, PNG, JPG"
                return JsonResponse(data)
        try:
            goal_data = data_set['goal_data']
            max_rate_data = data_set['max_rate_data']
            weight_data = data_set['weight_data']
            total_weight = 0
            for item in weight_data:
                total_weight += float(item['value'])
            if total_weight == 100:
                for item in goal_data:
                    KpiUserGoal.objects.filter(id=item['id']).update(goal_value=item['value'])
                for item in max_rate_data:
                    if float(item['value']) <= 1:
                        raise ValidationError("Minimum value for maximum finish rate is 1")
                    KpiUserGoal.objects.filter(id=item['id']).update(max_finish_rate=item['value'])
                for item in weight_data:
                    if float(item['value']) < 0:
                        raise ValidationError("Minimum value for weight is 0")
                    KpiUserGoal.objects.filter(id=item['id']).update(weight=item['value'])

                user_year = KpiUserYear.objects.select_related('user').prefetch_related('user_year_kpi_values').get(id=user_time_period)
                user_year.status = 4 #hod approved
                user_year.hod_signature = kpi_goal_hod_signature
                user_year.hod_signature_date = hod_signature_date
                user_year.approved_by = request.user
                user_year.approved_at = datetime.now()
                user_year.save()

                #notification
                              
                link = domainlink+'/my-kpi-goals/'               

                msg = f"{request.user.first_name} {request.user.last_name} has approved your kpi yearly goals"
                info = {}
                info['type'] = 'kpi_yearly_goal_activated'
                info['action'] = 'Create'
                info['action_id'] = user_year.id
                info['url'] = link
                notificationUpdate(user_from=request.user,
                                                    user_to=user_year.user,
                                                    message=msg, info=info)
                #email notification                
                mail_subject = 'KPI Yearly Goals'
                user_name = user_year.user.first_name+" "+user_year.user.last_name
                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI yearly goals - HOD approved","YEAR": datetime_timezone.now().year}
                send_email_to_users.delay(mail_subject, 4, content_replace, user_year.user.email)

                log_msg = f"Approved kpi yearly goals of {user_year.user.first_name} {user_year.user.last_name}"
                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_yearly_goal_activated'
                log_data['action_type']  = CREATE
                log_data['log_message']  = log_msg
                log_data['status']  = SUCCESS
                log_data['model_object']  = user_year
                log_data['db_data']  = {}
                log_data['app_visibility']  = True
                log_data['web_visibility']  = True
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/kpi-approve-goal/'  
                LogUserActivity(request, log_data)

                data['action'] = "Goal approved successfully"
                    
                context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                                filter(kpi_user_year_id=user_year.id,is_active=True).\
                                values('id','kpi_goal__kpi_category__name','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').\
                                    order_by('-kpi_goal__kpi_category__id','id')
                context['user_year'] = user_year
                template = loader.render_to_string("hisensehr/kpi/goalapprove/goals_approval_ajax.html",request=request,context=context)
            if radio == 'approve' and total_weight != 100:
                data['status'] = False
                data['message'] = "Total weight should be 100%"
            else:
                data['template'] = template
                data['status'] = True
        except Exception as e:
            print("......",e)
            log_msg = f"Approval of kpi yearly goals failed"
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_approve_goal'
            log_data['action_type']  = CREATE
            log_data['log_message']  = log_msg
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/kpi-approve-goal/'  
            LogUserActivity(request, log_data)
            
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class KpiSetView(LoginRequiredMixin,View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        if KpiActualTargetPermission.objects.filter(assigned_to=request.user,kpi_action_id__in=[5,6]).exists():
            data = {}
            encrypt = request.GET.get('encrypt', None)
            decrypted = None
            if encrypt:
                try:
                    decrypted = decrypt_me(encrypt)
                    data['decrypted'] = decrypted
                except:
                    raise Http404()
                            
            selected_period = request.GET.get('time_period')
            if selected_period:
                try:
                    KpiTimePeriod.objects.get(id=selected_period)
                except:
                    raise Http404()
            
            data['selected_period'] = selected_period
            data['time_periods'] = KpiTimePeriod.objects.filter(kpi_year__is_active=True,kpi_year__current_year=True,is_active=True)
            perm_added_user_ids = KpiActualTargetPermission.objects.filter(assigned_to=request.user,kpi_action_id__in=[5,6],kpi_user_goal__kpi_user_year__kpi_year__current_year=True).values_list('kpi_user_goal__kpi_user_year__user_id',flat=True).distinct()
            data['categories'] = KpiCategory.objects.filter(is_active=True).select_related('department').\
                        prefetch_related(Prefetch('kpi_goals',queryset=KpiGoal.objects.filter(is_active=True).select_related('measurement_type').order_by('id')))
            data['goals'] = KpiGoal.objects.filter(is_active=True).select_related()
            data['users'] = User.objects.filter(id__in=perm_added_user_ids,is_active=True).order_by(Lower("first_name"))
            data['actions'] = KpiAction.objects.filter(is_active=True,kpi_action_type=2).exclude(id__in=[3,4]).order_by("id")
            return renderfile(request,'kpi/setkpi','set_kpi_view',data)
        else:
            raise PermissionDenied()

class KpiTemplateView(LoginRequiredMixin,View):
    login_url = '/'
    def get_kpi_template(self,request,action,user,time_period):
        try:
            context = {}
            time_period_obj = KpiTimePeriod.objects.select_related('kpi_year').get(id=time_period)
            user_time_period = KpiUserTimePeriod.objects.select_related('kpi_user_year').filter(kpi_user_year__user_id=user,kpi_time_period_id=time_period).last()
            time_period_name = time_period_obj.name
            user_year = KpiUserYear.objects.select_related('user','user__profile').filter(user_id=user,kpi_year=time_period_obj.kpi_year).last()
            assigned_goals = KpiActualTargetPermission.objects.filter(assigned_to=request.user, kpi_user_goal__kpi_user_year__user_id = user , kpi_action_id=action).values_list('kpi_user_goal_id', flat=True)
            context['probation_completed'] = False
            context['selected_user'] = user_year.user
            joining_date =  user_year.user.profile.date_of_joining
            today = date.today()
            diff_months = (relativedelta.relativedelta(today, joining_date))
            diff_months = diff_months.months + (diff_months.years * 12)
            policy = user_year.user.profile.leave_policy
            if diff_months >= policy.probation_period:
                context['probation_completed'] = True

            if action == '5': #set target
                if user_year:
                    if user_year.status == 4:
                        context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                            filter(kpi_user_year_id=user_year.id,is_active=True, id__in=assigned_goals).\
                            values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                        if user_time_period:
                            context['user_goals_query_set'] = KpiUserGoal.objects.\
                                prefetch_related(Prefetch('user_target',queryset=KpiUserTarget.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id'))).\
                                filter(kpi_user_year_id=user_year.id,is_active=True,id__in=assigned_goals)
                        context['total_weight'] = context['user_goals'].aggregate(Sum('weight'))['weight__sum']
                context['current_period'] = time_period_name
                template = loader.render_to_string('hisensehr/kpi/setkpi/set_target.html',context=context,request=request)
            elif action == '6':#set actual
                if user_year and user_time_period:
                    if user_year.status == 4 and user_time_period.set_target:
                        context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                            filter(kpi_user_year_id=user_year.id,is_active=True, id__in=assigned_goals).\
                            values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                        if user_time_period:
                            context['user_goals_query_set'] = KpiUserGoal.objects.\
                                prefetch_related(Prefetch('user_actual',queryset=KpiUserActual.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id')),Prefetch('user_target',queryset=KpiUserTarget.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id'))).\
                                filter(kpi_user_year_id=user_year.id,is_active=True, id__in=assigned_goals)
                        context['total_weight'] = context['user_goals'].aggregate(Sum('weight'))['weight__sum']
                context['current_period'] = time_period_name
                template = loader.render_to_string('hisensehr/kpi/setkpi/set_actual.html',context=context,request=request)
            else:
                template = None
            
            return template
        except Exception as e:
            print(".......",e)
            #log entry
            log_data = {}
            log_data['module_name']  = 'get_action_template'
            log_data['action_type']  = READ
            log_data['log_message']  = ''
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/get-action-template/'
            LogUserActivity(request, log_data)
            return None

    def get(self,request,*args, **kwargs):
        action = request.GET.get('action')
        user = request.GET.get('user')
        period = request.GET.get('period')
        data = {}
        if KpiActualTargetPermission.objects.filter(assigned_to=request.user, kpi_user_goal__kpi_user_year__user_id = user , kpi_action_id=action).exists():
            template = self.get_kpi_template(request=request,action=action,user=user,time_period=period)
            data['status'] = True
            data['template'] = template
            return JsonResponse(data)
        else:
            data['status'] = False
            data['message'] = "You have no permission to do this action"
            return JsonResponse(data)


class DisableActionDropdownView(LoginRequiredMixin,View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        data = {}
        try:
            user = request.GET.get('user')
            perm_added_user_ids = KpiActualTargetPermission.objects.filter(assigned_to=request.user,kpi_user_goal__kpi_user_year__user_id=user,kpi_action__kpi_action_type=2).\
                values_list('kpi_action_id',flat=True).distinct()
            data['action_ids'] = json.dumps(list(perm_added_user_ids))
            data['status'] = True
        except Exception as e:
            print('......',e)
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_disable_action'
            log_data['action_type']  = READ
            log_data['log_message']  = ''
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/disable-action/'
            LogUserActivity(request, log_data)
            data['status'] = False
        return JsonResponse(data)


class SetTargetView(LoginRequiredMixin,View):
    login_url = '/' 
    def post(self,request,*args, **kwargs):
        data = {}
        context = {}
        try:
            with transaction.atomic():
                action = request.POST.get('action')
                user = request.POST.get('user')
                period = request.POST.get('period')
                target_list_string = request.POST.get('target')
                target_list = json.loads(target_list_string)
                time_period = KpiTimePeriod.objects.select_related('kpi_year').get(id=period)
                time_period_name = time_period.name
                user_year = KpiUserYear.objects.select_related('user','user__profile').filter(user_id=user,kpi_year=time_period.kpi_year).last()
                context['probation_completed'] = False
                context['selected_user'] = user_year.user
                joining_date =  user_year.user.profile.date_of_joining
                today = date.today()
                diff_months = (relativedelta.relativedelta(today, joining_date))
                diff_months = diff_months.months + (diff_months.years * 12)
                policy = user_year.user.profile.leave_policy
                if diff_months >= policy.probation_period:
                    context['probation_completed'] = True

                scheme = request.is_secure() and "https://" or "http://"
                current_site = get_current_site(request)
                domainlink=scheme+current_site.domain                
                            
                if user_year.status == 4: #approved
                    if KpiActualTargetPermission.objects.filter(assigned_to=request.user,kpi_user_goal__kpi_user_year__user_id=user,kpi_action_id=action).exists():
                        user_time_period = KpiUserTimePeriod.objects.update_or_create(kpi_time_period_id=period,kpi_user_year_id=user_year.id)#Pending ,defaults={'set_target':True}
                        if user_time_period[0]:
                            if user_time_period[0].status >= 4:
                                data['status'] = False
                                data['message'] = "Kpi already approved, you can not edit at this stage"
                                return JsonResponse(data)

                        for target in target_list:
                            if float(target['value']) < 0:
                                raise ValidationError("Minimum value for target is 0")
                            KpiUserTarget.objects.update_or_create(kpi_user_goal_id=target['goal_id'],kpi_user_time_period_id=user_time_period[0].id,defaults={'added_by':request.user,'target':target['value'],'remark':target['remark']})

                        status = 1 
                        #print(KpiUserTarget.objects.filter(kpi_user_time_period=user_time_period[0]).count(),'---------------',KpiUserGoal.objects.filter(kpi_user_year=user_year).count())
                        if KpiUserTarget.objects.filter(kpi_user_time_period=user_time_period[0]).count() == KpiUserGoal.objects.filter(kpi_user_year=user_year, is_active=True).count():
                            status = 2
                            user_time_period[0].set_target = True

                            #notification
                            link = domainlink+'/my-kpi/'
                            
                            msg = f"Target values of {time_period.name} has been set "
                            info = {}
                            info['type'] = 'kpi_target_set'
                            info['action'] = 'Create'
                            info['action_id'] = time_period.id
                            info['url'] = link
                            notificationUpdate(user_from=request.user,
                                                                user_to=user_year.user,
                                                                message=msg, info=info)
                            #email notification                            
                            mail_subject = 'KPI target values are set'
                            user_name = user_year.user.first_name+" "+user_year.user.last_name
                            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI target values have been set","YEAR": datetime_timezone.now().year}
                            send_email_to_users.delay(mail_subject, 4, content_replace, user_year.user.email)

                            log_msg = f"Target values of {time_period.name} has been set for {user_year.user.first_name} {user_year.user.last_name}"
                            #log entry                      
                            log_data = {}
                            log_data['module_name']  = 'kpi_target_set'
                            log_data['action_type']  = CREATE
                            log_data['log_message']  = log_msg
                            log_data['status']  = SUCCESS
                            log_data['model_object']  = time_period
                            log_data['db_data']  = {}
                            log_data['app_visibility']  = True
                            log_data['web_visibility']  = True
                            log_data['error_msg']  = ''
                            log_data['fwd_link']  = ''
                            LogUserActivity(request, log_data)
                            
                        user_time_period[0].status = status
                        user_time_period[0].save()

                        assigned_goals = KpiActualTargetPermission.objects.filter(assigned_to=request.user, kpi_user_goal__kpi_user_year__user_id = user , kpi_action_id=action).values_list('kpi_user_goal_id', flat=True)
            
                        context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                            filter(kpi_user_year_id=user_year.id,is_active=True, id__in=assigned_goals ).\
                            values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                        if user_time_period[0]:
                            context['user_goals_query_set'] = KpiUserGoal.objects.\
                                prefetch_related(Prefetch('user_target',queryset=KpiUserTarget.objects.filter(kpi_user_time_period_id=user_time_period[0].id).order_by('-id'))).\
                                filter(kpi_user_year_id=user_year.id,is_active=True, id__in=assigned_goals)
                        context['total_weight'] = context['user_goals'].aggregate(Sum('weight'))['weight__sum']
                        context['current_period'] = time_period_name

                        template = loader.render_to_string('hisensehr/kpi/setkpi/set_target.html',context=context,request=request)
                        data['status'] = True
                        data['template'] = template
                    else:
                        data['status'] = False
                        data['message'] = "You have no permission to do this action"
                else:
                    data['status'] = False
                    data['message'] = "Goals not approved"

        except ValidationError as e:
            data['status'] = False
            data['message'] = e.message
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_target_set'
            log_data['action_type']  = CREATE
            log_data['log_message']  = "Target values setting failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e.message
            log_data['fwd_link']  = ''
            LogUserActivity(request, log_data)

        except Exception as e:
            print(".....",e)
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_target_set'
            log_data['action_type']  = CREATE
            log_data['log_message']  = "Target values setting failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = ''
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class SetActualView(LoginRequiredMixin,View):
    login_url = '/' 
    def post(self,request,*args, **kwargs):
        data = {}
        context = {}
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain
        try:
            with transaction.atomic():
                action = request.POST.get('action')
                user = request.POST.get('user')
                period = request.POST.get('period')
                actual_list_string = request.POST.get('actual')
                actual_list = json.loads(actual_list_string)
                time_period = KpiTimePeriod.objects.select_related('kpi_year').get(id=period)
                time_period_name = time_period.name
                user_year = KpiUserYear.objects.select_related('user','user__profile').filter(user_id=user,kpi_year=time_period.kpi_year).last()
                context['probation_completed'] = False
                context['selected_user'] = user_year.user
                joining_date =  user_year.user.profile.date_of_joining
                today = date.today()
                diff_months = (relativedelta.relativedelta(today, joining_date))
                diff_months = diff_months.months + (diff_months.years * 12)
                policy = user_year.user.profile.leave_policy
                if diff_months >= policy.probation_period:
                    context['probation_completed'] = True

                user_time_period =KpiUserTimePeriod.objects.select_related('kpi_user_year').filter(kpi_user_year__user_id=user,kpi_time_period_id=period).last()
                                             
                if user_time_period:
                    if user_year.status == 4 and user_time_period.set_target:
                        if KpiActualTargetPermission.objects.filter(assigned_to=request.user,kpi_user_goal__kpi_user_year__user_id=user,kpi_action_id=action).exists():
                            if user_time_period.status >= 4:
                                data['status'] = False
                                data['message'] = "Kpi already approved, you can not edit at this stage"
                                return JsonResponse(data)
                            for actual in actual_list:
                                if float(actual['value']) < 0:
                                    raise ValidationError("Minimum value for actual is 0")
                                KpiUserActual.objects.update_or_create(kpi_user_goal_id=actual['goal_id'],kpi_user_time_period_id=user_time_period.id,defaults={'added_by':request.user,'actual':actual['value'],'remark':actual['remark']})
                            status = 2 
                            #print(KpiUserActual.objects.filter(kpi_user_time_period=user_time_period).count(),'---------------',KpiUserGoal.objects.filter(kpi_user_year=user_year).count())
                            if KpiUserActual.objects.filter(kpi_user_time_period=user_time_period).count() == KpiUserGoal.objects.filter(kpi_user_year=user_year, is_active=True).count():
                                status = 3
                                user_time_period.set_actual = True

                                #notification
                                link = domainlink+'/my-kpi/'
                                
                                msg = f"Actual values of {time_period.name} has been set , waiting for your approval"
                                info = {}
                                info['type'] = 'kpi_actual_set'
                                info['action'] = 'Create'
                                info['action_id'] = time_period.id
                                info['url'] = link
                                notificationUpdate(user_from=request.user,
                                                                    user_to=user_year.user,
                                                                    message=msg, info=info)
                                #email notification                                
                                mail_subject = 'KPI actual values are set'
                                user_name = user_year.user.first_name+" "+user_year.user.last_name
                                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI actual values have been set","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, user_year.user.email)

                                log_msg = f"Actual values of {time_period.name} has been set for {user_year.user.first_name} {user_year.user.last_name}"
                                #log entry                      
                                log_data = {}
                                log_data['module_name']  = 'kpi_actual_set'
                                log_data['action_type']  = CREATE
                                log_data['log_message']  = log_msg
                                log_data['status']  = SUCCESS
                                log_data['model_object']  = time_period
                                log_data['db_data']  = {}
                                log_data['app_visibility']  = True
                                log_data['web_visibility']  = True
                                log_data['error_msg']  = ''
                                log_data['fwd_link']  = '/kpi-time-periods/'
                                LogUserActivity(request, log_data)

                            user_time_period.status = status
                            user_time_period.save()

                            assigned_goals = KpiActualTargetPermission.objects.filter(assigned_to=request.user, kpi_user_goal__kpi_user_year__user_id = user , kpi_action_id=action).values_list('kpi_user_goal_id', flat=True)
                            
                            context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                                filter(kpi_user_year_id=user_year.id,is_active=True, id__in=assigned_goals).\
                                values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                            context['user_goals_query_set'] = KpiUserGoal.objects.\
                                prefetch_related(Prefetch('user_actual',queryset=KpiUserActual.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id')),Prefetch('user_target',queryset=KpiUserTarget.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id'))).\
                                filter(kpi_user_year_id=user_year.id,is_active=True, id__in=assigned_goals)
                            
                            context['total_weight'] = context['user_goals'].aggregate(Sum('weight'))['weight__sum']
                            context['current_period'] = time_period_name
                            context['total_weight'] = context['user_goals_query_set'].aggregate(Sum('weight'))['weight__sum']

                            
                            # scheme = request.is_secure() and "https://" or "http://"
                            # current_site = get_current_site(request)
                            # domainlink=scheme+current_site.domain
                            # mail_subject = 'Workflow - KPI'
                            # link=domainlink+f"/employee-view/?time_period={period}"
                            # message = f"Actual values added for you on the time period {user_time_period.kpi_time_period.name}. Waiting for your approval"    
                            # content_replace={"MESSAGE":message,"LINK":link,"TITLE":"KPI Approval"}
                            # emailhelper(request,mail_subject,1,content_replace,user_time_period.user.email)


                            template = loader.render_to_string('hisensehr/kpi/setkpi/set_actual.html',context=context,request=request)
                            data['status'] = True
                            data['template'] = template
                        else:
                            data['status'] = False
                            data['message'] = "You have no permission to do this action"
                    else:
                        data['status'] = False
                        data['message'] = "Target values are not set or goals are not approved"
                else:
                    data['status'] = False
                    data['message'] = "Target values are not set or goals are not approved"

        except ValidationError as e:
            data['status'] = False
            data['message'] = e.message
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_actual_set'
            log_data['action_type']  = CREATE
            log_data['log_message']  = "Actual values setting failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e.message
            log_data['fwd_link']  = '/kpi-time-periods/'
            LogUserActivity(request, log_data)

        except Exception as e:
            print(".....",e)
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_actual_set'
            log_data['action_type']  = CREATE
            log_data['log_message']  = "Actual values setting failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/kpi-time-periods/'
            LogUserActivity(request, log_data)
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class KpiSelfApprove(LoginRequiredMixin, View):
    login_url = '/'
    def post(self,request,*args, **kwargs):
        data = {}
        user_time_period = request.POST.get("user_time_period")
        kpi_employee_signature = request.FILES.get("kpi_employee_signature")
        employee_signature_date = datetime.today().date()
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain                
                
        if kpi_employee_signature:
            extension = kpi_employee_signature.name.split('.')[-1].upper()
            if not extension in ['JPEG','PNG', 'JPG']:
                data['status'] = False
                data['message'] = "Invalid signature file type, allowed extentions are JPEG, PNG, JPG"
                return JsonResponse(data)
        try:
            with transaction.atomic():
                user_time_period = KpiUserTimePeriod.objects.select_related('kpi_time_period').filter(id=user_time_period).order_by('-id').first()
                user_time_period.status = 4 #employee approved
                user_time_period.employee_signature = kpi_employee_signature
                user_time_period.employee_signature_date = employee_signature_date
                user_time_period.save()

                #notification
                #link = domainlink+'/kpi-time-periods/'
                link = domainlink+'/hod-view/?time_period='+str(user_time_period.kpi_time_period_id)+'&encrypt='+encrypt_me(request.user.pk)
                
                assigned_users = HeadOfDepartment.objects.select_related('user').filter(department_id=request.user.profile.department_id)
                msg = f"{user_time_period.kpi_time_period.name} kpi has been approved by {request.user.first_name} {request.user.last_name}, waiting for your approval"
                info = {}
                info['type'] = 'kpi_emp_approved'
                info['action'] = 'Create'
                info['action_id'] = user_time_period.id
                info['user_id'] = user_time_period.kpi_user_year.user_id
                info['url'] = link
                info['kpi_time_period_id'] = user_time_period.kpi_time_period_id
                info['end_user'] = request.user.pk
                for assigned_user in assigned_users:
                    notificationUpdate(user_from=request.user,
                                                        user_to=assigned_user.user,
                                                        message=msg, info=info)
                    #email notification                    
                    mail_subject = 'KPI Employee Approved'
                    user_name = assigned_user.user.first_name+" "+assigned_user.user.last_name
                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI employee approved","YEAR": datetime_timezone.now().year}
                    send_email_to_users.delay(mail_subject, 4, content_replace, assigned_user.user.email)

                log_msg = f"Approved your {user_time_period.kpi_time_period.name} kpi"
                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_emp_approved'
                log_data['action_type']  = CREATE
                log_data['log_message']  = log_msg
                log_data['status']  = SUCCESS
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = True
                log_data['web_visibility']  = True
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/my-kpi/'
                LogUserActivity(request, log_data)
                
                data['status'] = True
        except Exception as e:
            print("...===...",e)
            #log entry                      
            log_data = {}
            log_data['module_name']  = 'kpi_emp_approved'
            log_data['action_type']  = CREATE
            log_data['log_message']  = "KPI approval failed"
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = e
            log_data['fwd_link']  = '/my-kpi/'
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class HODView(LoginRequiredMixin,View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        if HeadOfDepartment.objects.filter(user=request.user,is_active=True).exists():
            data = {}
            encrypt = request.GET.get('encrypt', None)
            decrypted = None
            if encrypt:
                try:
                    decrypted = decrypt_me(encrypt)
                    data['decrypted'] = decrypted
                except:
                    raise Http404()
            selected_period = request.GET.get("time_period")
            try:
                KpiTimePeriod.objects.get(id=selected_period)
            except:
                raise Http404()
            department_ids = HeadOfDepartment.objects.filter(is_active=True,user=request.user).values_list('department_id',flat=True)
            data['users'] = User.objects.exclude(is_superuser=True).filter(is_active=True,profile__department_id__in=department_ids).\
                order_by(Lower('first_name'))
            data['time_periods'] = KpiTimePeriod.objects.filter(kpi_year__is_active=True,kpi_year__current_year=True,is_active=True)            
            data['selected_period'] = selected_period
            return renderfile(request,'kpi/hod','kpi_hod_view',data)
        else:
            raise PermissionDenied()

    def post(self,request,*args, **kwargs):
        if HeadOfDepartment.objects.filter(user=request.user,is_active=True).exists():
            data = {}
            context = {}
            scheme = request.is_secure() and "https://" or "http://"
            current_site = get_current_site(request)
            domainlink = scheme+current_site.domain
                    
            try:
                period = request.POST.get("period")
                user = request.POST.get("user")
                signature = request.FILES.get("hod_signature")
                if signature:
                    extension = signature.name.split('.')[-1].upper()
                    if not extension in ['JPEG','PNG', 'JPG']:
                        data['status'] = False
                        data['message'] = "Invalid signature file type, allowed extentions are JPEG, PNG, JPG"
                        return JsonResponse(data)
        
                if period:
                    user_time_period = KpiUserTimePeriod.objects.select_related('kpi_time_period').filter(kpi_time_period_id=period,kpi_user_year__user_id=user).order_by('-id').first()
                    user_time_period.hod_date = date.today()
                    user_time_period.hod_signature = signature
                    user_time_period.status = 5 #HOD approved
                    user_time_period.approved_by = request.user
                    user_time_period.save()

                    #notification
                    link = domainlink+'/my-kpi/'

                    assignee = User.objects.filter(id = user).first()
                    msg = f"{request.user.first_name} {request.user.last_name} has approved your {user_time_period.kpi_time_period.name} KPI"
                    info = {}
                    info['type'] = 'kpi_hod_approved'
                    info['action'] = 'Create'
                    info['action_id'] = user_time_period.id
                    info['assignee'] = user
                    info['hod'] = request.user.pk
                    info['url'] = link
                    notificationUpdate(user_from=request.user,
                                                        user_to=assignee,
                                                        message=msg, info=info)
                    #email notification    
                    mail_subject = 'KPI HOD approved'                                    
                    user_name = assignee.first_name+" "+assignee.last_name
                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI HOD approved","YEAR": datetime_timezone.now().year}
                    send_email_to_users.delay(mail_subject, 4, content_replace, assignee.email)

                    
                    log_msg = f"Approved KPI of {assignee.first_name} {assignee.last_name}"
                    #log entry                      
                    log_data = {}
                    log_data['module_name']  = 'kpi_hod_approved'
                    log_data['action_type']  = CREATE
                    log_data['log_message']  = log_msg
                    log_data['status']  = SUCCESS
                    log_data['model_object']  = user_time_period
                    log_data['db_data']  = {}
                    log_data['app_visibility']  = True
                    log_data['web_visibility']  = True
                    log_data['error_msg']  = ''
                    log_data['fwd_link']  = '/hod-view/'
                    LogUserActivity(request, log_data)

                    context['user_time_period'] = user_time_period
                    template = loader.render_to_string("hisensehr/kpi/hod/hod_signature.html",request=request,context=context)
                    data['template'] = template
                    data['status'] = True
                else:
                    data['status'] = False
                    data['message'] = "something went wrong"
            except Exception as e:
                print(".....",e)
                #log entry                      
                log_data = {}
                log_data['module_name']  = 'kpi_hod_approved'
                log_data['action_type']  = CREATE
                log_data['log_message']  = 'KPI approval failed'
                log_data['status']  = FAILED
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = False
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/hod-view/'
                LogUserActivity(request, log_data)

                data['status'] = False
                data['message'] = "something went wrong"
            return JsonResponse(data)
        else:
            raise PermissionDenied()


class LoadHodTemplateView(LoginRequiredMixin,View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        if HeadOfDepartment.objects.filter(user=request.user,is_active=True).exists():
            data = {}
            context = {}
            try:
                period = request.GET.get('period')
                user = request.GET.get('user')
                time_period = KpiTimePeriod.objects.get(id=period)
                user_time_period = KpiUserTimePeriod.objects.select_related('kpi_user_year__user').filter(kpi_time_period_id=period,kpi_user_year__user_id=user).order_by('-id').first()
                user_year = KpiUserYear.objects.filter(user_id=user,kpi_year=time_period.kpi_year).last()
                context['current_period'] = time_period.name
                context['selected_user'] = User.objects.get(id=user)
                if user_time_period and user_year:
                    if user_time_period.set_actual and user_year.status == 4:
                        context['guideline'] = get_kpi_guideline(user, time_period.kpi_year_id)
                        context['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                            filter(kpi_user_year_id=user_year.id,is_active=True).\
                            values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__measurement_type__calculation','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                        context['user_goals_query_set'] = KpiUserGoal.objects.\
                            prefetch_related(Prefetch('user_actual',queryset=KpiUserActual.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id')),Prefetch('user_target',queryset=KpiUserTarget.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id'))).\
                            filter(kpi_user_year_id=user_year.id,is_active=True)
                        context['total_weight'] = context['user_goals_query_set'].aggregate(Sum('weight'))['weight__sum']
                        context['user_time_period'] = user_time_period
                        context['today'] = date.today()
                        context['selected_user'] = user
                template = loader.render_to_string("hisensehr/kpi/hod/hod_ajax.html",request=request,context=context)
                data['status'] = True
                data['template'] = template
            except Exception as e:
                print("....",e)
                #log entry
                log_data = {}
                log_data['module_name']  = 'kpi_hod_template'
                log_data['action_type']  = READ
                log_data['log_message']  = ''
                log_data['status']  = FAILED
                log_data['model_object']  = None
                log_data['db_data']  = {}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = False
                log_data['error_msg']  = e
                log_data['fwd_link']  = '/hod-template/'
                LogUserActivity(request, log_data)
                data['status'] = False
                data['message'] = "something went wrong"
            return JsonResponse(data)
        else:
            raise PermissionDenied()

class EmployeeKpiView(LoginRequiredMixin,View):
    login_url = '/' 
    def get(self,request,*args, **kwargs):
        period = request.GET.get('time_period')            
        try:   
            time_period = KpiTimePeriod.objects.get(id=period)
        except:
            raise Http404
        try:      
            data = {}   
            user_time_period = KpiUserTimePeriod.objects.select_related('kpi_user_year__user','approved_by','kpi_time_period').filter(kpi_time_period_id=period,kpi_user_year__user_id=request.user.id,kpi_approval_status='approved').order_by('-id').first()
            if user_time_period != None:
                user_year = KpiUserYear.objects.filter(user_id=request.user.id,kpi_year=time_period.kpi_year).last()
                
                # Check if updated_score is not None and get the modified log
                # modified_log = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=user_time_period)
                modified_log = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=user_time_period)
                if modified_log:
                    # group the modified log based on the value type(cricism and appreciation)
                    criticism_list = [log for log in modified_log if log.value_type == 1]
                    appreciation_list = [log for log in modified_log if log.value_type == 2]
                    # sum up the values for both criticism and appreciation
                    criticism_value_sum = sum(log.value for log in criticism_list)
                    appreciation_value_sum = sum(log.value for log in appreciation_list)
                    # pass thosee values to the html
                    data['criticism_list'] = criticism_list
                    data['appreciation_list'] = appreciation_list
                    data['criticism_value_sum'] = criticism_value_sum
                    data['appreciation_value_sum'] = appreciation_value_sum
                if modified_log:
                    modified_log= modified_log.last()
                else:
                    modified_log = PerformanceModificationLog.objects.none()

                data['current_user'] = User.objects.select_related('profile','profile__department','profile__designation').get(id=request.user.id)
                # print(user_time_period.id,"..........")
                data['current_period'] = time_period.name
                data['current_period_id'] = time_period.id
                # if user_year:
                #     if user_year.status == 4:
                data['guideline'] = get_kpi_guideline(request.user.id, time_period.kpi_year_id)
                data['user_goals'] = KPIUserGoalValues.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','kpiuser_goal','kpiuser_goal__kpi_user_year','kpiuser_goal__kpi_user_year__user','kpiuser_goal__kpi_goal__measurement_type','kpiuser_goal__kpi_goal__kpi_category')\
                    .filter(kpiuser_timeperiod__kpi_time_period=period,kpiuser_goal__kpi_user_year__user=request.user)
                if user_time_period:
                    data['user_goals_query_set'] = KpiUserGoal.objects.\
                            prefetch_related(Prefetch('user_actual',queryset=KpiUserActual.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id')),Prefetch('user_target',queryset=KpiUserTarget.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id'))).\
                            filter(kpi_user_year_id=user_year.id,is_active=True)
                    
                data['user_time_period'] = user_time_period
                data['modified_log'] = modified_log
                data['total_weight'] = data['user_goals'].aggregate(Sum('weight'))['weight__sum']
        except:
            data['current_user'] = User.objects.select_related('profile','profile__department','profile__designation').get(id=request.user.id)
        data['current_user'] = User.objects.select_related('profile','profile__department','profile__designation').get(id=request.user.id)
        return renderfile(request,'kpi/employee','kpi_user_view',data)
    
class MyGoalsPdf(LoginRequiredMixin,View):
    login_url = '/'
    def get(self,request,*args, **kwargs):        
        try:
            data = {}
            period = kwargs['year']
            user_year = KpiUserYear.objects.filter(user_id=request.user.pk,kpi_year_id=period, status__gte = 2).last()
            data['current_user'] = User.objects.select_related('profile','profile__department','profile__designation').get(id=request.user.id)
            data['user_year'] = user_year
            if user_year:
                data['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                            filter(kpi_user_year_id=user_year.id,is_active=True).\
                            values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__measurement_type__calculation','kpi_goal__name','goal_value','max_finish_rate','weight').\
                                order_by('-kpi_goal__kpi_category__id','id')
            
            template = loader.get_template( 'hisensehr/pdf/goal_pdf.html' )
            html_string  = template.render(data)
            pdf_file = HTML(string=html_string,base_url=request.build_absolute_uri()).write_pdf()
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'filename="pdf_name.pdf"'
            response['Content-Transfer-Encoding'] = 'utf-8'
            return response          
        except Exception as ex:
            print("---ex----",ex)
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_my_goals_pdf'
            log_data['action_type']  = READ
            log_data['log_message']  = ''
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = ex
            log_data['fwd_link']  = '/my-goals-pdf/'
            LogUserActivity(request, log_data)



def clean_html_and_add_bullets(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')
    processed_lines = []
    for element in soup.find_all(['ul', 'li', 'p']):
        if element.name == 'ul':
            for li_tag in element.find_all('li'):
                li_text = li_tag.get_text(strip=True)
                processed_lines.append(f"• {li_text}\n\n")  # Add bullet point with two newlines for each list item
        if element.name == 'p':
            p_text = element.get_text(strip=True)
            processed_lines.append(p_text + '\n\n')  # Add paragraphs with two newlines
    
    # Merge all lines into a single string
    merged_text = ''.join(processed_lines)
    
    return merged_text                     

class MyKpiPdf(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpiusertarget'

    def get(self, request, *args, **kwargs):        
        try:
            period = kwargs['timeperiod']
            usr = kwargs['usr']
            time_period = KpiTimePeriod.objects.get(id=period)
            user_time_period = KpiUserTimePeriod.objects.select_related(
                'kpi_user_year__user', 'approved_by', 'kpi_time_period'
            ).filter(
                kpi_time_period_id=period, kpi_user_year__user_id=usr
            ).order_by('-id').first()
            year = user_time_period.kpi_user_year.kpi_year.name
            user = User.objects.select_related('profile', 'profile__department', 'profile__designation').get(id=usr)
            guideline = get_kpi_guideline(user.id, time_period.kpi_year_id)
            current_period = time_period.name
            user_goals = KPIUserGoalValues.objects.select_related(
                    'kpiuser_timeperiod', 'kpiuser_timeperiod__kpi_time_period',
                    'kpiuser_goal', 'kpiuser_goal__kpi_user_year', 
                    'kpiuser_goal__kpi_user_year__user', 'kpiuser_goal__kpi_goal__measurement_type',
                    'kpiuser_goal__kpi_goal__kpi_category'
                ).filter(
                    kpiuser_timeperiod__kpi_time_period=time_period.id,
                    kpiuser_goal__kpi_user_year__user=user
                )
            timeperiod_name = 'Quarterly' if time_period.kpi_year.time_period == 1 else 'Half Yearly' if time_period.kpi_year.time_period == 2 else 'Yearly'
            user_heading = f"{timeperiod_name} KPI - {user.profile.department.name} {user.first_name} {user.last_name}"
            user_info = {
                "Full Name": f"{user.first_name} {user.last_name}",
                "Department": user.profile.department.name,
                "Title": user.profile.designation.name
            }

            kpi_data = []
            for goal in user_goals:
                achieved_percentage = round(get_achieved_percentage(goal.actual, goal.target, goal.max_finish_rate, goal.kpiuser_goal.kpi_goal.measurement_type.calculation), 2)
                weightage_score = round(get_weightage_score(goal.actual, goal.target, goal.weight, goal.max_finish_rate, goal.kpiuser_goal.kpi_goal.measurement_type.calculation), 2)
                kpi_goal = goal.kpiuser_goal.kpi_goal.kpi_category.name
                measurement = goal.kpiuser_goal.kpi_goal.measurement_type.name
                kpi_name = f"{goal.kpiuser_goal.kpi_goal.name}:{goal.goal_value}; Max Finish Rate ={goal.max_finish_rate}%"
                weight = f"{goal.weight}%"
                target = goal.target
                actual = goal.actual
                achieved_percentage = f"{achieved_percentage}%"
                weightage_score = f"{weightage_score}%"
                remarks = goal.remark

                kpi_data.append({
                    "KPI": kpi_goal,
                    "Measurement": measurement,
                    "Goal": kpi_name,
                    "Weight": weight,
                    f"Target {current_period}": target,
                    f"Actual {current_period}": actual,
                    "% Achieved(Actual / Target)": achieved_percentage,
                    "Weightage Score(%Achieved x Weightage%)": weightage_score,
                    "Remarks": remarks
                })

            df_heading = pd.DataFrame([user_heading])
            df_user_info = pd.DataFrame([user_info])
            df_kpi_data = pd.DataFrame(kpi_data)

            if guideline is not None and hasattr(guideline, 'content'):
                df_guideline = pd.DataFrame([{"KPI - Guidelines": guideline.content}])
            else:
                df_guideline = pd.DataFrame([{"KPI - Guidelines": "No guideline available"}])

            df_achievement_rules = pd.DataFrame([
                {"KPI Achievement": "80% or above", "Bonus Payout %": "Company KPI*30% + Personal KPI*70%"},
                {"KPI Achievement": "Less than 80%", "Bonus Payout %": "Management Decision"}
            ])
            total_score = user_goals[0].kpiuser_timeperiod.score if user_goals else 0
            updated_total_score = user_goals[0].kpiuser_timeperiod.updated_score
            appreciation_list, criticism_list, appreciation_value_sum = None, None, None
            # get the modified log data based on user timeperiod
            performance_modified_log = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=user_goals[0].kpiuser_timeperiod)
            if performance_modified_log:
                criticism_list = [log for log in performance_modified_log if log.value_type == 1]
                appreciation_list = [log for log in performance_modified_log if log.value_type == 2]
                # sum up the values for both criticism and appreciation
                criticism_value_sum = sum(log.value for log in criticism_list)
                appreciation_value_sum = sum(log.value for log in appreciation_list)
            
            filename = '{0}_{1}_KPIPerformance_{2} {3}.xlsx'.format(year, current_period, user.first_name, user.last_name)
            file_path = os.path.join('/tmp', filename)  # Use a temporary directory or change this to the desired path

            with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                start_row = 0
                # Define formats
                left_align_format = writer.book.add_format({'align': 'left', 'valign': 'vcenter', 'border': 1})
                center_bold_font = writer.book.add_format({'align': 'center', 'valign': 'vcenter', 'bold': True, 'font_size': 14, 'border': 1})
                bold_format = writer.book.add_format({'bold': True, 'border': 1})
                wrap_format = writer.book.add_format({'text_wrap': True, 'border': 1})
                merge_format = writer.book.add_format({'align': 'center', 'valign': 'vcenter', 'bold': True, 'border': 1})
                merge_format_align_right = writer.book.add_format({'align': 'right', 'valign': 'right', 'bold': True, 'border': 1})
                merge_format_no_bold = writer.book.add_format({'align': 'right', 'valign': 'right', 'bold': False, 'border': 1})
                border_fmt = writer.book.add_format({'border': 1})
                border_fmt_bold = writer.book.add_format({'border': 1, 'bold': True})

                # Write heading with borders
                df_heading.to_excel(writer, sheet_name='KPI Report', index=False, startrow=start_row, header=False)
                worksheet = writer.sheets['KPI Report']
                worksheet.set_row(start_row, 20)  # Set the height of the row for the heading
                for col_num, value in enumerate(df_heading.columns.values):
                    worksheet.write(start_row, 8, '', border_fmt)
                # for row_num in range(len(df_heading)):
                #     for col_num, value in enumerate(df_heading.iloc[row_num]):
                #         worksheet.write(start_row + 1 + row_num, col_num, value, border_fmt)
                # worksheet.write(start_row, 8,'', border_fmt_bold)

                start_row += len(df_heading) + 1

                # Write user info headers and merge columns up to H, align left with borders
                user_info_headers = list(user_info.keys())
                user_info_values = list(user_info.values())
                for col_num, header in enumerate(user_info_headers):
                    worksheet.write(start_row, col_num, header, bold_format)
                # Ensure border up to 8th column
                for col_num in range(9):
                    worksheet.write(start_row, col_num, user_info_headers[col_num] if col_num < len(user_info_headers) else '', border_fmt_bold)
                worksheet.set_row(start_row, 20)  # Set the height of the row for user info headers
                for col_num, value in enumerate(user_info_values):
                    worksheet.write(start_row + 1, col_num, value, left_align_format)
                # Ensure border up to 8th column
                for col_num in range(9):
                    worksheet.write(start_row + 1, col_num, user_info_values[col_num] if col_num < len(user_info_values) else '', border_fmt)
                worksheet.set_row(start_row + 1, 20)  # Set the height of the row for user info values
                start_row += 2
                # Write KPI data with borders
                df_kpi_data.to_excel(writer, sheet_name='KPI Report', index=False, startrow=start_row, header=False)
                for col_num, value in enumerate(df_kpi_data.columns.values):
                    worksheet.write(start_row, col_num, value, border_fmt_bold)
                worksheet.set_row(start_row, 20)  # Set the height of the row for KPI data headers
                for row_num in range(len(df_kpi_data)):
                    for col_num, value in enumerate(df_kpi_data.iloc[row_num]):
                        worksheet.write(start_row + 1 + row_num, col_num, value, border_fmt)
                    worksheet.set_row(start_row + 1 + row_num, 20)  # Set the height of each row in KPI data
                start_row += len(df_kpi_data) + 1

                # Add total score row with "Total" text centered and columns merged with borders
                total_score_df = pd.DataFrame([["", "", "", "", "", "", "", f"{total_score}%"]])
                worksheet.merge_range(start_row, 0, start_row, 6, 'Total', merge_format_align_right)
                worksheet.write(start_row, 7, total_score_df.iloc[0, 7], border_fmt_bold)
                worksheet.write(start_row, 8,'', border_fmt_bold)
                worksheet.set_row(start_row, 20)  # Set the height of the row for total score
                start_row += len(total_score_df)

                # modified apreciation log to the sheet
                if appreciation_list:
                    total_score_df = pd.DataFrame([["", "", "", "", "", "", "", f"{appreciation_value_sum}%"]])
                    worksheet.merge_range(start_row, 0, start_row, 6, '{}'.format('Letter of praise'), merge_format_align_right)
                    worksheet.write(start_row, 7, total_score_df.iloc[0, 7], border_fmt_bold)
                    worksheet.write(start_row, 8,'', border_fmt_bold)   
                    worksheet.set_row(start_row, 20)  # Set the height of the row for total score
                    start_row += len(total_score_df)
                    for apriciation_log in appreciation_list:
                        total_score_df = pd.DataFrame([["", "", "", "", "", "", "", f"{apriciation_log.value}%"]])
                        worksheet.merge_range(start_row, 0, start_row, 6, '{} (Dated on: {}'.format(apriciation_log.comments, apriciation_log.modification_date), merge_format_no_bold)
                        worksheet.write(start_row, 7, total_score_df.iloc[0,7], border_fmt)
                        worksheet.write(start_row, 8,'', border_fmt_bold)
                        worksheet.set_row(start_row, 20)  # Set the height of the row for total score
                        start_row += len(total_score_df)

                # modified criticism log to the sheet
                if criticism_list:
                    total_score_df = pd.DataFrame([["", "", "", "", "", "", "", f"{criticism_value_sum}%"]])
                    worksheet.merge_range(start_row, 0, start_row, 6, '{}'.format('Letter of criticism'), merge_format_align_right)
                    worksheet.write(start_row, 7, total_score_df.iloc[0, 7], border_fmt_bold)
                    worksheet.write(start_row, 8,'', border_fmt_bold)
                    worksheet.set_row(start_row, 20)  # Set the height of the row for total score
                    start_row += len(total_score_df)
                    for criticism_log in criticism_list:
                        total_score_df = pd.DataFrame([["", "", "", "", "", "", "", f"{criticism_log.value}%"]])
                        worksheet.merge_range(start_row, 0, start_row, 6, '{} (Dated on: {})'.format(criticism_log.comments, criticism_log.modification_date), merge_format_no_bold)
                        worksheet.write(start_row, 7, total_score_df.iloc[0, 7], border_fmt)
                        worksheet.write(start_row, 8,'', border_fmt_bold)
                        worksheet.set_row(start_row, 20)  # Set the height of the row for total score
                        start_row += len(total_score_df)

                if updated_total_score is not None:
                    if user_goals and user_goals[0].kpiuser_timeperiod.updated_score is not None:
                        modified_log = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=user_goals[0].kpiuser_timeperiod)
                        if modified_log:
                            modified_log= modified_log.last()
                    else:
                        modified_log = None
                    value_type = 'Modified Total'
                    # Add total score row with "Total" text centered and columns merged with borders
                    total_score_df = pd.DataFrame([["", "", "", "", "", "", "", f"{updated_total_score}%"]])
                    worksheet.merge_range(start_row, 0, start_row, 6, 'Modified Total'.format(value_type), merge_format_align_right)
                    worksheet.write(start_row, 7, total_score_df.iloc[0, 7], border_fmt_bold)
                    worksheet.write(start_row, 8,'', border_fmt_bold)
                    worksheet.set_row(start_row, 20)  # Set the height of the row for total score
                    start_row += len(total_score_df) + 1

                # Add borders to the empty row before KPI guideline data
                for col_num in range(9):  # Assuming there are 8 columns
                    worksheet.write(start_row, col_num, '', border_fmt)
                worksheet.set_row(start_row, 20)  # Set the height of the empty row
                start_row += 1

                # Write guideline with borders
                guideline_start_row = start_row
                df_guideline.to_excel(writer, sheet_name='KPI Report', index=False, startrow=start_row, header=False)
                for col_num, value in enumerate(df_guideline.columns.values):
                    worksheet.write(start_row, col_num, value, border_fmt)
                worksheet.set_row(start_row, 20)  # Set the height of the row for guideline header
                for row_num in range(len(df_guideline)):
                    for col_num, value in enumerate(df_guideline.iloc[row_num]):
                        worksheet.write(start_row + 1 + row_num, col_num, value, border_fmt)
                    worksheet.set_row(start_row + 1 + row_num, 20)  # Set the height of each row in guideline data
                start_row += len(df_guideline) + 1

                # Write achievement rules with column merging and borders
                achievement_rules_start_row = start_row
                worksheet.write(achievement_rules_start_row, 0, "KPI Achievement", bold_format)
                worksheet.write(achievement_rules_start_row, 3, "Bonus Payout %", bold_format)
                worksheet.set_row(achievement_rules_start_row, 20)  # Set the height of the row for achievement rules header
                worksheet.merge_range(achievement_rules_start_row + 1, 0, achievement_rules_start_row + 1, 2, df_achievement_rules.iloc[0, 0], border_fmt)
                worksheet.merge_range(achievement_rules_start_row + 1, 3, achievement_rules_start_row + 1, 8, df_achievement_rules.iloc[0, 1], border_fmt)
                worksheet.set_row(achievement_rules_start_row + 1, 20)  # Set the height of the row for first achievement rule
                worksheet.merge_range(achievement_rules_start_row + 2, 0, achievement_rules_start_row + 2, 2, df_achievement_rules.iloc[1, 0], border_fmt)
                worksheet.merge_range(achievement_rules_start_row + 2, 3, achievement_rules_start_row + 2, 8, df_achievement_rules.iloc[1, 1], border_fmt)
                worksheet.set_row(achievement_rules_start_row + 2, 20)  # Set the height of the row for second achievement rule
                start_row += len(df_achievement_rules) + 1

                # Write the user_heading with the specified format and borders
                worksheet.merge_range('A1:H1', user_heading, center_bold_font)
                worksheet.set_row(0, 30)  # Set the height of the row for the main heading

                guideline_text = guideline.content if guideline else "No guideline available"
                guideline_text = clean_html_and_add_bullets(guideline_text)

                # Merge cells for the guideline heading, apply bold format and wrap format
                worksheet.merge_range(guideline_start_row, 0, guideline_start_row, 8, df_guideline.columns[0], bold_format)

                # Merge cells for the guideline content
                worksheet.merge_range(guideline_start_row + 1, 0, guideline_start_row + 1, 8, guideline_text, wrap_format)

                # Adjust the row height to fit the text
                text_length = len(guideline_text)
                max_characters_per_line = 80  # Estimated max number of characters per line
                num_lines = text_length // max_characters_per_line + 1
                row_height = num_lines * 15  # Adjust 15 as per your font size and cell padding

                worksheet.set_row(guideline_start_row + 1, row_height)

                static_column_widths = {
                    0: 35,  # Name column
                    1: 25,  # Department column
                    2: 20,  # Title column (if needed)
                }

                # Autofit columns for KPI data
                for i, col in enumerate(df_kpi_data.columns):
                    if i in static_column_widths:
                        # Use predefined width for specific columns
                        worksheet.set_column(i, i, static_column_widths[i])
                    else:
                        max_length = max(df_kpi_data[col].astype(str).map(len).max(), len(col)) + 2  # Adding some padding
                        worksheet.set_column(i, i, max_length)

                # Protect the worksheet
                worksheet.protect()

            if os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    response = HttpResponse(f.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                    response['Content-Disposition'] = f'attachment; filename={filename}'
                    return response
            else:
                return HttpResponse("Error creating the file", status=500)

        except Exception as ex:
            import traceback;traceback.print_exc()
            log_data = {
                'module_name': 'my_kpi_pdf',
                'action_type': 'READ',
                'log_message': '',
                'status': 'FAILED',
                'model_object': None,
                'db_data': {},
                'app_visibility': False,
                'web_visibility': False,
                'error_msg': str(ex),
                'fwd_link': '/my-kpi-pdf/',
            }
            LogUserActivity(request, log_data)
            return HttpResponse(status=500)

class EmployeeGoalsPdf(LoginRequiredMixin,View):
    login_url = '/'
    def get(self,request,*args, **kwargs):        
        try:
            data = {}
            user = request.GET.get('user')
            time_period = request.GET.get('time_period')
            user_year = KpiUserYear.objects.filter(user_id=user,kpi_year_id=time_period).last()
            data['current_user'] = User.objects.select_related('profile','profile__department','profile__designation').get(id=user)
            data['user_year'] = user_year
            if user_year:
                data['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                            filter(kpi_user_year_id=user_year.id,is_active=True).\
                            values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').\
                                order_by('-kpi_goal__kpi_category__id','id')
            
            template = loader.get_template( 'hisensehr/pdf/goal_pdf.html' )
            html_string  = template.render(data)
            pdf_file = HTML(string=html_string,base_url=request.build_absolute_uri()).write_pdf()
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'filename="pdf_name.pdf"'
            response['Content-Transfer-Encoding'] = 'utf-8'
            return response          
        except Exception as ex:
            print("---ex----",ex)
            #log entry
            log_data = {}
            log_data['module_name']  = 'employee_goals_pdf'
            log_data['action_type']  = READ
            log_data['log_message']  = ''
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = ex
            log_data['fwd_link']  = '/employee-goals-pdf/'
            LogUserActivity(request, log_data)

class EmployeeKpiPdf(LoginRequiredMixin,View):
    login_url = '/'
    def get(self,request,*args, **kwargs):        
        try:
            data = {}
            period = request.GET.get('time_period')
            user = request.GET.get('user')
            time_period = KpiTimePeriod.objects.get(id=period)
            user_time_period = KpiUserTimePeriod.objects.select_related('kpi_user_year__user').filter(kpi_time_period_id=period,kpi_user_year__user_id=user).order_by('-id').first()
            user_year = KpiUserYear.objects.filter(user_id=user,kpi_year=time_period.kpi_year).last()
            data['current_user'] = User.objects.select_related('profile','profile__department','profile__designation').get(id=request.user.id)
            data['current_period'] = time_period.name
            if user_year:
                if user_year.status == 4:
                    data['guideline'] = get_kpi_guideline(user, time_period.kpi_year_id)
                    data['user_goals'] = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_goal__measurement_type').\
                        filter(kpi_user_year_id=user_year.id,is_active=True).\
                        values('kpi_goal__kpi_category__name','id','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('-kpi_goal__kpi_category__id','id')
                    if user_time_period:
                        data['user_goals_query_set'] = KpiUserGoal.objects.\
                                prefetch_related(Prefetch('user_actual',queryset=KpiUserActual.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id')),Prefetch('user_target',queryset=KpiUserTarget.objects.filter(kpi_user_time_period_id=user_time_period.id).order_by('-id'))).\
                                filter(kpi_user_year_id=user_year.id,is_active=True)
                    data['user_time_period'] = user_time_period
                    data['total_weight'] = data['user_goals'].aggregate(Sum('weight'))['weight__sum']
        
            template = loader.get_template( 'hisensehr/pdf/kpi_pdf.html' )
            html_string  = template.render(data)
            pdf_file = HTML(string=html_string,base_url=request.build_absolute_uri()).write_pdf()
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'filename="pdf_name.pdf"'
            response['Content-Transfer-Encoding'] = 'utf-8'
            return response          
        except Exception as ex:
            print("---ex----",ex)
            #log entry
            log_data = {}
            log_data['module_name']  = 'employee_kpi_pdf'
            log_data['action_type']  = READ
            log_data['log_message']  = ''
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = ex
            log_data['fwd_link']  = '/employee-kpi-pdf/'
            LogUserActivity(request, log_data)

# download employee goal sheet
class ExportEmployeeGoals(LoginRequiredMixin,View):
    login_url = '/'

    def has_permission(self):
        # Check if the user has access to the dynamic menu
        import_export_name = 'employee_kpi'
        menu = Dynamic_menus.objects.filter(title_slug=import_export_name,is_active=True).last()
        if menu:
            menu_id = menu.id
            user_groups = self.request.user.groups.values_list('id', flat=True)[0]
            has_access = Group_menu_access.objects.filter(group_id=user_groups, dynamic_menu_id=menu_id).exists()
            return True if has_access else False
        else:
            return False
    
    def get(self,request,*args, **kwargs): 
        error_message = None
        try:
            department_id = request.GET.getlist('department[]')
            employee_ids = request.GET.getlist('employee_ids[]')
            timeperiod_id = request.GET.get('timeperiod')
            timeperiod = KpiTimePeriod.objects.filter(id=timeperiod_id, is_active=True).last()

            # # Check if the user has HR or user-level permissions
            has_hr_permission = self.has_permission()

            # # Fetch reporting employees based on KPIActionPermission
            reporting_prsn_emp = KpiActionPermission.objects.select_related(
                'assigned_to', 'kpi_action', 'department', 'user'
            ).filter(
                kpi_action__key_action__in=['target_settings', 'actual_settings'],
                department__is_active=True
            )

            # # If the user has HR permissions, include all employees in the department
            if has_hr_permission:
                if department_id:
                    department_ids = [int(id) for id in department_id]
                    reporting_prsn_emp = reporting_prsn_emp.filter(department_id__in=department_ids)
            else:
                # Filter based on the user's assigned permissions
                reporting_prsn_emp = reporting_prsn_emp.filter(assigned_to=request.user)

            # # Further filter by department and employee IDs
            if department_id:
                department_ids = [int(id) for id in department_id]
                reporting_prsn_emp = reporting_prsn_emp.filter(department_id__in=department_ids)

            if employee_ids:
                reporting_prsn_emp = reporting_prsn_emp.filter(user_id__in=employee_ids)

            unique_user_ids = set(reporting_prsn_emp.values_list('user_id', flat=True))
            profiles = Profile.objects.select_related('user').filter(user_id__in=unique_user_ids)

            # Initialize workbook and data dictionary before any early returns
            workbook = Workbook()
            data = {'status': True}
            no_data = False

            if not has_hr_permission and not profiles.exists():
                data['status'] = False
                data['message'] = "You don't have permission to view these employee goals. Please ensure you have the standard role permission enabled."
                return JsonResponse(data)

            if employee_ids:
                profiles = profiles.filter(user_id__in=employee_ids)            

            employee_ids = set()
            for emp in profiles:
                User_goal_status = KpiUserYear.objects.select_related('user','kpi_year').filter(user = emp.user_id,kpi_year__current_year=True)
                user_id_encoded = base64.b64encode(str(emp.user_id).encode()).decode()
                MAX_SHEET_LENGTH = 31
                # Reserve space for employee_id, hyphens, and encoded_id
                RESERVED_SPACE = len(emp.employee_id) + 2 + 4  # 2 for hyphens, 4 for encoded id
                # Calculate available space for full name
                available_name_space = MAX_SHEET_LENGTH - RESERVED_SPACE
                # Combine full name
                full_name = f"{emp.user.first_name} {emp.user.last_name}".split()
                final_name_parts = []
                current_length = 0
                
                for part in full_name:
                    # +1 for the space between parts
                    if current_length + len(part) + (1 if final_name_parts else 0) <= available_name_space:
                        final_name_parts.append(part)
                        current_length += len(part) + (1 if len(final_name_parts) > 1 else 0)
                    else:
                        break
                
                # Join the name parts we could fit
                final_name = " ".join(final_name_parts)

                # Create sheet name
                sheet_name = f"{emp.employee_id}-{final_name}-{user_id_encoded}"
                sheet = workbook.create_sheet(sheet_name)
                # sheet = workbook.create_sheet(title=emp.employee_id+'-'+emp.user.first_name + ' ' + emp.user.last_name+'-'+user_id_encoded)
                sheet.row_dimensions[1].height = 30
                sheet.row_dimensions[2].height = 20
            
                # common heading
                emp_name = emp.user.first_name.upper() + ' ' + emp.user.last_name.upper()
                emp_dept = emp.department.name.upper()
                current_year = KpiYear.objects.get(current_year=True)
                title_prefix = "Mr." if emp.gender == 1 else "Mrs." if emp.marital_status == 2 else "Ms."
                common_heading = "KPI YEARLY {0},{1} DEPARTMENT - {2} {3}".format(current_year.name,emp_dept,title_prefix,emp_name)
                
                # for the common heading
                sheet.merge_cells("A1:M1")
                merged_cell = sheet.cell(row=1, column=1)
                merged_cell.value = common_heading
                alignment = Alignment(horizontal='center', vertical='center')
                merged_cell.alignment = alignment
                header_row = ["Name", "Dept.", "Title", "KPI", "Measurement", "Code", "Goal", "Weight", "Goal value", "Maximum finish rate","Target value","Actual value","Remarks"]
                sheet.append(header_row)
                data = []
                if User_goal_status and User_goal_status.first().status == 3:
                    total_weight = 0 
                    data_flag = 0
                    ending_row = 3    # Initialize ending row
                    if KpiUserYear.objects.select_related('user').filter(user=emp.user_id): #to fetch weight and max finish rate
                        data_flag = 1
                        kpi_user_year = KpiUserYear.objects.select_related('user').filter(user=emp.user_id)
                        is_first_row = True  # Flag to indicate the first row
                        if KpiUserGoal.objects.select_related('kpi_user_year').filter(kpi_user_year=kpi_user_year.last()):
                            no_data = True
                            kpi_user_goal = KpiUserGoal.objects.select_related('kpi_user_year').prefetch_related(
                                Prefetch('user_actual', queryset=KpiUserActual.objects.select_related('kpi_user_time_period','kpi_user_time_period__kpi_time_period').order_by('id')),
                                Prefetch('user_target', queryset=KpiUserTarget.objects.select_related('kpi_user_time_period','kpi_user_time_period__kpi_time_period').order_by('id'))
                            ).filter(kpi_user_year=kpi_user_year.last(),is_active=True).order_by('id')

                            for goal_weight in kpi_user_goal:
                                if goal_weight.weight != None:
                                    total_weight += goal_weight.weight
                                user_actual_objects = goal_weight.user_actual.select_related('kpi_user_time_period','kpi_user_time_period__kpi_time_period').filter(kpi_user_time_period__kpi_time_period_id=int(timeperiod_id)).last() 
                                user_target_objects = goal_weight.user_target.select_related('kpi_user_time_period','kpi_user_time_period__kpi_time_period').filter(kpi_user_time_period__kpi_time_period_id=int(timeperiod_id)).last() 

                                if user_actual_objects:
                                    remarks = user_actual_objects.remark
                                    actual = user_actual_objects.actual
                                else:
                                    remarks = ""
                                    actual = ""

                                if user_target_objects:
                                    target = user_target_objects.target
                                else:
                                    target = ""

                                if is_first_row:
                                    data_row = [
                                        title_prefix+' '+emp.user.first_name + ' ' + emp.user.last_name,
                                        emp.department.name,
                                        emp.designation.name,
                                        goal_weight.kpi_goal.kpi_category.name,
                                        goal_weight.kpi_goal.measurement_type.name,
                                        base64.b64encode(str(goal_weight.id).encode()).decode(),
                                        goal_weight.kpi_goal.name,
                                        goal_weight.weight,
                                        goal_weight.goal_value,
                                        goal_weight.max_finish_rate,
                                        target,
                                        actual,
                                        remarks
                                    ]
                                    
                                    data.append(data_row)
                                    is_first_row = False
                                    ending_row += 1
                                else:
                                    data_row = [
                                        "", 
                                        "", 
                                        "", 
                                        goal_weight.kpi_goal.kpi_category.name,
                                        goal_weight.kpi_goal.measurement_type.name,
                                        base64.b64encode(str(goal_weight.id).encode()).decode(),
                                        goal_weight.kpi_goal.name,
                                        goal_weight.weight,
                                        goal_weight.goal_value,
                                        goal_weight.max_finish_rate,
                                        target,
                                        actual,
                                        remarks
                                    ]

                                    data.append(data_row)
                                    ending_row += 1
                    
                        # Add data to the sheet
                        for row in data:
                            sheet.append(row)


                        result_row = ["", "", "", "", "", "", "", total_weight, "", "", "", "", "", ""]
                        sheet.append(result_row)




                    
                        if data_flag == 0:
                            sheet.merge_cells("A{0}:M{0}".format(ending_row))  # Assuming "nodata_error" row is the last row
                            # Create a new cell for the "nodata_error" row
                            nodata_error = sheet.cell(row=ending_row, column=1)
                            nodata_error.value = "KPI has not been set for this employee."
                            nodata_error.font = Font(bold=True)

                            # Set the alignment for the "nodata_error" 
                            alignment = Alignment(horizontal='center', vertical='center')
                            nodata_error.alignment = alignment
                        else:
                            # Merge all the cells in the total row
                            sheet.merge_cells("A{0}:G{0}".format(ending_row))  # Assuming "Total" row is the last row

                            # Create a new cell for the "Total" row
                            total_cell = sheet.cell(row=ending_row, column=1)
                            total_cell.value = "Total"
                            total_cell.font = Font(bold=True)

                            # Set the alignment for the "Total" cell
                            alignment = Alignment(horizontal='center', vertical='center')
                            total_cell.alignment = alignment
                                
                            # Set borders for the cell
                            border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
                            total_cell.border = border

                        # Set the row height for data rows
                        for i in range(3, ending_row + 1):
                            sheet.row_dimensions[i].height = 20

                        # Set the column width for each column
                        column_widths = {
                            'A': 50,
                            'B': 50,
                            'C': 25,
                            'D': 30,
                            'E': 15,
                            'F': 10,
                            'G': 50,
                            'H': 8,
                            'I': 10,
                            'J': 10,
                            'K': 15,
                            'L': 15,
                            'M': 30,
                        }

                        for col, width in column_widths.items():
                            sheet.column_dimensions[col].width = width

                        # Create a DataValidation object for numbers
                        dv = DataValidation(type="decimal", operator="greaterThanOrEqual", formula1="0")
                        dv.errorStyle = 'stop'
                        dv.showErrorMessage = True
                        dv.errorTitle = 'Invalid Input'
                        dv.error = 'Please enter a number'

                        # Add the data validation to the sheet
                        sheet.add_data_validation(dv)

                        for row in range(1, ending_row + 2):
                            for col_num, col in enumerate(['A', 'B', 'C', 'D', 'E', 'F', 'G','H','I','J','K','L','M']):
                                sheet.protection.sheet = True
                                cell = sheet.cell(row=row, column=col_num + 1)  # Adjust the column number
                                
                                # Set alignment to center
                                cell.alignment = Alignment(horizontal='center', vertical='center')
                                
                                # Set borders for the cell
                                border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
                                cell.border = border
                                
                                if col in ['K', 'L']:
                                    cell.protection = Protection(locked=False)  # Unlock cells in columns K and L
                                    # Add data validation to only allow numbers
                                    dv.add(cell)

                                if  col != 'K' and col != 'L' and col != 'M':
                                    cell.protection = Protection(locked=True) #Lock only code column
                                else:
                                    cell.protection = Protection(locked=False)  # Lock all other cells
                                
                            # merge rows
                            if no_data == True: #if kpi goals added then only merge
                                if row == 3:
                                    for col in range(1, 4):  # Merge from column A to C
                                        sheet.merge_cells(start_row=3, start_column=col, end_row=ending_row-1, end_column=col)
                        sheet.protection.sheet = True
                        sheet.protection.enable()
                        # Merge cells for the "KPI Guidelines" row
                        merged_cell_range = "A{0}:I{0}".format(ending_row + 1)
                        sheet.merge_cells(merged_cell_range)

                        # Set the value for the merged cell
                        merged_cell = sheet.cell(row=ending_row + 1, column=1)
                        merged_cell.value = "KPI Guidelines"

                        # Apply font and border to the merged cell
                        merged_cell.font = Font(bold=True)

                        # Create a border with only the bottom side
                        border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
                        merged_cell.border = border

                        # Adjust row height
                        sheet.row_dimensions[ending_row + 1].height = 30

                        # Set the alignment and word wrapping for the "guidlineText"
                        alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                        merged_cell.alignment = alignment

                        try:
                            kpi_guideline = KpiGuideline.objects.get(is_active=True,kpi_year = current_year).content
                        except KpiGuideline.DoesNotExist:
                            kpi_guideline = ''
                        soup = BeautifulSoup(kpi_guideline, 'html.parser')
                        cleaned_text = soup.get_text()
                        # kpi_guideline_lines = cleaned_text.split('\n')

                        if cleaned_text:
                            ending_row += 2
                            guidline_start_row = ending_row

                            sheet.merge_cells("A{0}:I{0}".format(ending_row))
                            guidlineText = sheet.cell(row=ending_row, column=1)
                            
                            # Assign the cleaned_text to guidlineText.value
                            guidlineText.value = cleaned_text

                            # Set the alignment and word wrapping for the "guidlineText"
                            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                            guidlineText.alignment = alignment

                            # Set the row height for data rows
                            sheet.row_dimensions[ending_row].height = 200

                            # Apply border to cells from J{0} to M{0}
                            for col in range(10, 14):  # J is the 10th column, M is the 13th column
                                cell = sheet.cell(row=ending_row, column=col)
                                cell.border = border


                            # static content
                            border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

                            # Merge cells for the "KPI Guidelines" row
                            merged_cell_range = "A{0}:D{1}".format(ending_row + 1, ending_row + 3)
                            sheet.merge_cells(merged_cell_range)

                            # Set the value for the merged cell
                            merged_cell = sheet.cell(row=ending_row + 1, column=1)
                            merged_cell.value = ("Your bonus percentage as termed in your offer letter will be based on your KPI achievement & performance evaluation. "
                                                "Bonus payout % will be based on the column illustrated aside. However, all bonus payments are subject to overall KPI evaluation and as decided by the management. "
                                                "This document for the bonus process will supersede all previous documents, letters issued before.")

                            # Adjust row height
                            sheet.row_dimensions[ending_row + 1].height = 40

                            # Set the alignment and word wrapping for the "guidlineText"
                            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                            merged_cell.alignment = alignment

                            # Apply the border to the merged cell range
                            for row in sheet.iter_rows(min_row=ending_row + 1, max_row=ending_row + 3, min_col=1, max_col=4):
                                for cell in row:
                                    cell.border = border


                            # Merge cells for the "KPI Guidelines" row for "KPI Achievement"
                            merged_cell_range2 = "E{0}:F{0}".format(ending_row + 1)
                            sheet.merge_cells(merged_cell_range2)

                            # Set the value for the merged cell
                            merged_cell2 = sheet.cell(row=ending_row + 1, column=5)
                            merged_cell2.value = "KPI Achievement"

                            # Adjust row height
                            sheet.row_dimensions[ending_row + 1].height = 30

                            # Set the alignment and word wrapping for the "guidelineText"
                            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                            merged_cell2.alignment = alignment

                            # Create a border with all sides
                            border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

                            # Apply the border to the merged cells E{0}:F{0}
                            for col in range(5, 7):  # E is the 5th column, F is the 6th column
                                cell = sheet.cell(row=ending_row + 1, column=col)
                                cell.border = border

                            # Merge cells for the "KPI Guidelines" row for "Bonus Payout %"
                            merged_cell_range3 = "G{0}:I{0}".format(ending_row + 1)
                            sheet.merge_cells(merged_cell_range3)

                            # Set the value for the merged cell
                            merged_cell3 = sheet.cell(row=ending_row + 1, column=7)
                            merged_cell3.value = "Bonus Payout %"

                            # Set the alignment and word wrapping for the "guidelineText"
                            merged_cell3.alignment = alignment

                            # Apply the border to the merged cells G{0}:I{0}
                            for col in range(7, 10):  # G is the 7th column, I is the 9th column
                                cell = sheet.cell(row=ending_row + 1, column=col)
                                cell.border = border

                            # Apply the border to the cells from I{0} to M{0}
                            for col in range(9, 14):  # I is the 9th column, M is the 13th column
                                cell = sheet.cell(row=ending_row + 1, column=col)
                                cell.border = border

        

                            # next row
                            ending_row+=1

                            # Merge cells for the "KPI Guidelines" row
                            merged_cell_range2 = "E{0}:F{0}".format(ending_row + 1)
                            sheet.merge_cells(merged_cell_range2)

                            # Set the value for the merged cell
                            merged_cell2 = sheet.cell(row=ending_row + 1, column=5)
                            merged_cell2.value = "80% or above"

                            # Adjust row height
                            sheet.row_dimensions[ending_row + 1].height = 50

                            # Set the alignment and word wrapping for the "guidlineText"
                            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                            merged_cell2.alignment = alignment


                            # Merge cells for the "KPI Guidelines" row
                            merged_cell_range2 = "G{0}:I{0}".format(ending_row + 1)
                            sheet.merge_cells(merged_cell_range2)

                            # Set the value for the merged cell
                            merged_cell2 = sheet.cell(row=ending_row + 1, column=7)
                            merged_cell2.value = "Company KPI*30% + Personal KPI*70%"

                            # Adjust row height
                            sheet.row_dimensions[ending_row + 1].height = 50

                            # Set the alignment and word wrapping for the "guidlineText"
                            alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
                            merged_cell2.alignment = alignment

                            # Assuming `ending_row` is defined and refers to the correct row number
                            ending_row += 1

                            # Merge and set values for columns E:F
                            merged_cell_range2 = "E{0}:F{0}".format(ending_row + 1)
                            sheet.merge_cells(merged_cell_range2)
                            merged_cell2 = sheet.cell(row=ending_row + 1, column=5)
                            merged_cell2.value = "Less than 80%"
                            merged_cell2.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

                            # Merge and set values for columns G:I
                            merged_cell_range2 = "G{0}:I{0}".format(ending_row + 1)
                            sheet.merge_cells(merged_cell_range2)
                            merged_cell2 = sheet.cell(row=ending_row + 1, column=7)
                            merged_cell2.value = "Management Decision"
                            merged_cell2.alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

                            # Adjust row height
                            sheet.row_dimensions[ending_row + 1].height = 30

                            guidline_end_row = ending_row

                            # Define the border style
                            border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

                            # Apply borders for the range from J to M for the specified row
                            for col_num in range(10, 14):  # Columns J to M are 10 to 13
                                cell = sheet.cell(row=ending_row + 1, column=col_num)
                                cell.border = border


                            # Define the border style
                            border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
                            # Apply borders for the entire row from A to M
                            for col_num in range(1, 14):  # Columns A to M are 1 to 13
                                cell = sheet.cell(row=ending_row + 1, column=col_num)
                                cell.border = border
                            # Apply borders for the entire row from A to M
                            for row_num in range(ending_row, guidline_end_row + 1):
                                for col_num in range(1, 14):  # Columns A to M are 1 to 13
                                    cell = sheet.cell(row=row_num, column=col_num)
                                    cell.border = border

                        employee_ids.add(emp.id) #add to set

                    else:
                        sheet.merge_cells("A{0}:M{0}".format(ending_row))

                        # Create a new cell for the "nodata_error" row
                        nodata_error = sheet.cell(row=ending_row, column=1)
                        nodata_error.value = "KPI has not been set for this employee."

                        # Set the alignment for the "nodata_error"
                        alignment = Alignment(horizontal='center', vertical='center')
                        nodata_error.alignment = alignment

                        # Set the row height for data rows
                        for i in range(3, ending_row + 1):
                            sheet.row_dimensions[i].height = 20

                        # Set the column width for each column
                        column_widths = {
                            'A': 15,
                            'B': 8,
                            'C': 15,
                            'D': 30,
                            'E': 15,
                            'F': 10,
                            'G': 30,
                            'H': 8,
                            'I': 10,
                            'J': 10,
                            'K': 10,
                            'L': 10,
                            'M': 30,
                        }

                        for col, width in column_widths.items():
                            sheet.column_dimensions[col].width = width

                        sheet.protection.sheet = True
                        cell = sheet.cell(row=row, column=col_num + 1)  # Adjust the column number
                        
                        # Set alignment to center
                        cell.alignment = Alignment(horizontal='center', vertical='center')
                        
                
                        if col == 'F' and row >= 3:
                            cell.protection = Protection(locked=True) #Lock only code column
                        else:
                            cell.protection = Protection(locked=False)  # Lock all other cells
                                    
                        # Set borders for the entire row
                        for row_num in range(ending_row, ending_row + 1):
                            for col_num in range(1, 14):  # Assuming you have 13 columns
                                cell = sheet.cell(row=row_num, column=col_num)
                                
                                # Set borders for the cell
                                border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))
                                cell.border = border

                    # hide the column F - code and J - max finish rate
                    sheet.column_dimensions['F'].hidden = True
                    sheet.column_dimensions['J'].hidden = True
                else:
                    # Add data to the sheet
                    for row in data:
                        sheet.append(row)
                    
                    # Set the column width for each column
                    column_widths = {
                        'A': 15,
                        'B': 8,
                        'C': 15,
                        'D': 30,
                        'E': 15,
                        'F': 10,
                        'G': 30,
                        'H': 8,
                        'I': 10,
                        'J': 10,
                        'K': 10,
                        'L': 10,
                        'M': 30,
                    }

                    for col, width in column_widths.items():
                        sheet.column_dimensions[col].width = width

                    # Define the border style
                    thin_border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )

                    no_data_message = "Goal approval is incomplete, or goal has not been set."

                    # Merge cells and set the message
                    sheet.merge_cells("A3:M3")
                    sheet.protection.sheet = True
                    sheet.protection.enable()
                    merged_cell = sheet.cell(row=3, column=1)
                    merged_cell.value = no_data_message
                    alignment = Alignment(horizontal='center', vertical='center')
                    merged_cell.alignment = alignment

                    # Apply the border to the merged cell
                    for row in sheet.iter_rows(min_row=2, max_row=3, min_col=1, max_col=13):
                        for cell in row:
                            cell.border = thin_border
                            alignment = Alignment(horizontal='center', vertical='center')
                            cell.alignment = alignment

                    # Set the row height for data rows
                    for i in range(2,4):
                        sheet.row_dimensions[i].height = 25

                

        except Exception as e:
            error_message = str(e) 
            
        # Remove the default sheet created and save the Excel file
        workbook.remove(workbook.active)
        # save to Activity log and Exportlog table
        info = {}
        info['employees'] = list(employee_ids)
        info['error_msg'] = error_message
        log_data = {}
        log_data['module_name'] = 'download_kpi'
        log_data['action_type'] = READ
        log_data['log_message'] = 'Downloaded the employee KPI goals.'
        log_data['status'] = SUCCESS
        log_data['model_object'] = None
        log_data['db_data'] = info
        log_data['app_visibility'] = False
        log_data['web_visibility'] = False
        log_data['error_msg'] = error_message
        log_data['fwd_link'] = ''
        log_data['activity_mode'] = 'Web'
        LogUserActivity(request, log_data)
        KPIExportLog.objects.create(file_name=None,actor=request.user,kpi_timeperiod=None,action_type=IMPORT,info=info)
        # Create an HttpResponse to return the Excel file
        response = HttpResponse(content_type='application/ms-excel')
        response['Content-Disposition'] = 'attachment; filename="{}_{}_KPI_EmployeeGoals.xlsx"'.format(timeperiod.kpi_year.name, timeperiod.name)
        workbook.save(response)
        return response

# export sheet back    
class ImportEmployeeGoalsVerify(LoginRequiredMixin,View):
    login_url = '/'

    @transaction.atomic
    def post(self,request,*args, **kwargs):
        employee_ids = set()
        uploaded_file = request.FILES.get('file',None)
        time_period = KpiTimePeriod.objects.get(id=int(request.POST['timeperiod']))
        numeric_pattern = re.compile(r'^\d+(\.\d+)?$')
        if uploaded_file:
            try:
                excel_data = openpyxl.load_workbook(uploaded_file)

                # check whether to do validation or save to table 
                if request.POST['calltype'] == 'validateFile':
                    error_messages = {}

                    for sheet_name in excel_data.sheetnames:
                        user_id_decoded = 0
                        sheetnamee_split = sheet_name.split("-")
                        if len(sheetnamee_split) > 2:
                            # name = " - ".join(sheetnamee_split[2:])
                            name = sheetnamee_split[-1]
                            user_id_decoded = int(base64.b64decode(name.encode()).decode())
                            employee_ids.add(user_id_decoded) #add to set
                        errors = []
                        sheet = excel_data[sheet_name]
                        header_row = sheet[2]
                        actual_value_column_index = None
                        target_value_column_index = None
                        remarks_value_column_index = None

                        for cell in header_row:
                            if cell.value == 'Actual value':
                                actual_value_column_index = cell.column
                            if cell.value == 'Target value':
                                target_value_column_index = cell.column
                            if cell.value == 'Remarks':
                                remarks_value_column_index = cell.column

                        for row in sheet.iter_rows(values_only=True):
                            if row[actual_value_column_index - 1] == 'Actual value':
                                continue
                            if row[target_value_column_index - 1] == 'Target value':
                                continue
                            if row[remarks_value_column_index - 1] == 'Remarks':
                                continue

                            actual_value = row[actual_value_column_index - 1]
                            target_value = row[target_value_column_index - 1]
                            remarks_value = row[remarks_value_column_index - 1]

                            # Convert the cell value to a string and check if it's numeric
                            if actual_value is not None and actual_value != '':
                                actual_value_str = str(actual_value)
                                # if not actual_value_str.isnumeric():
                                if not numeric_pattern.match(actual_value_str):
                                    errors.append(f"Non-numeric value encountered in Actual value: {actual_value_str}")
                            # Convert the cell value to a string and check if it's numeric
                            if target_value is not None and target_value != '':
                                target_value_str = str(target_value)
                                # if not target_value_str.isnumeric():
                                if not numeric_pattern.match(target_value_str):
                                    errors.append(f"Non-numeric value encountered in Target value: {target_value_str}")

                        if errors:  # If there are errors for this sheet, store them in the dictionary
                            error_messages[sheet_name] = errors

                    result = []
                    if error_messages:
                        result = {}
                        for key, values in error_messages.items():
                            result[key] = ', '.join(values)

                    if result:
                        info = {}
                        info['employees'] = list(employee_ids)
                        user_name = request.user.first_name +' '+ request.user.last_name
                        message = f"{user_name} tried to upload a file: '{uploaded_file.name}' on {timezone.now()} in {time_period.name}."
                        log_data = {}
                        log_data['module_name'] = 'upload_kpi'
                        log_data['action_type'] = CREATE
                        log_data['log_message'] = message
                        log_data['status'] = FAILED
                        log_data['model_object'] = None
                        log_data['db_data'] = info
                        log_data['app_visibility'] = False
                        log_data['web_visibility'] = False
                        log_data['error_msg'] = result
                        log_data['fwd_link'] = ''
                        log_data['activity_mode'] = 'Web'
                        LogUserActivity(request, log_data)
                        info['error_msg'] = message,result
                        KPIExportLog.objects.create(file_name=uploaded_file.name,actor=request.user,kpi_timeperiod=time_period,action_type=EXPORT,info=info,status=FAILED)
                        return JsonResponse({'result': 'error', 'errorData': result})
                        
                    else:
                        return JsonResponse({'result': 'success'})
                    
                elif request.POST['calltype'] == 'saveData':
                    with transaction.atomic():
                        for sheet_name in excel_data.sheetnames:
                            sheet = excel_data[sheet_name]
                            sheetnamee_split = sheet_name.split("-")
                            user_id_decoded = 0
                            if len(sheetnamee_split) > 2:
                                name = sheetnamee_split[-1]
                                # name = " - ".join(sheetnamee_split[2:]) 
                                user_id_decoded = int(base64.b64decode(name.encode()).decode())
                                employee_ids.add(user_id_decoded) #add to set
                            header_row = sheet[2]

                            # checking for the goal approval is done for the user if done no need to update sheet
                            user_year = KpiUserYear.objects.select_related('user','user__profile','kpi_year').filter(user_id=user_id_decoded,kpi_year_id=time_period.kpi_year,kpi_year__current_year=True).last()
                            if user_year and user_year.status == 3:
                                user_time_period = KpiUserTimePeriod.objects.select_related('kpi_time_period','kpi_user_year').filter(kpi_time_period=time_period,kpi_user_year=user_year)
                                if not user_time_period or user_time_period.first().kpi_approval_status not in ['approved', 'inprogress']:
                                    # User_goal_status = KpiUserYear.objects.select_related('user','kpi_year').filter(user = user_id_decoded,kpi_year__current_year=True)
                                    actual_value_column_index = None
                                    target_value_column_index = None
                                    remarks_value_column_index = None
                                    goal_value_column_index = None
                                    goal_values = None
                                    max_finish_rate_column_index = None
                                    weight_column_index = None
                                    Measurement_column_index = None
                                    for cell in header_row:
                                        if cell.value == 'Actual value':
                                            actual_value_column_index = cell.column
                                        if cell.value == 'Target value':
                                            target_value_column_index = cell.column
                                        if cell.value == 'Remarks':
                                            remarks_value_column_index = cell.column
                                        if cell.value == 'Code':
                                            goal_value_column_index = cell.column
                                        if cell.value == 'Goal value':
                                            goal_values = cell.column
                                        if cell.value == 'Maximum finish rate':
                                            max_finish_rate_column_index = cell.column
                                        if cell.value == 'Weight':
                                            weight_column_index = cell.column
                                        if cell.value == 'Measurement':
                                            Measurement_column_index = cell.column

                                    # check goals that need to remove after adding new goals
                                    KPIUserGoalValues.objects.filter(kpiuser_timeperiod__kpi_time_period=time_period, kpiuser_timeperiod__kpi_user_year__user_id=user_id_decoded).delete()
                                    total_weightage = 0
                                    for row in sheet.iter_rows(values_only=True):
                                        if row[actual_value_column_index - 1] == 'Actual value':
                                            continue
                                        if row[target_value_column_index - 1] == 'Target value':
                                            continue
                                        if row[remarks_value_column_index - 1] == 'Remarks':
                                            continue
                                        if row[goal_value_column_index - 1] == 'Code':
                                            continue
                                        if row[goal_values - 1] == 'Goal value':
                                            continue
                                        if row[max_finish_rate_column_index - 1] == 'Maximum finish rate':
                                            continue
                                        if row[weight_column_index - 1] == 'Weight':
                                            continue
                                        if row[Measurement_column_index - 1] == 'Measurement':
                                            continue


                                        actual_value = row[actual_value_column_index - 1]
                                        target_value = row[target_value_column_index - 1]
                                        remarks_value = row[remarks_value_column_index - 1]
                                        goal_value = row[goal_value_column_index - 1]
                                        goalValue = row[goal_values - 1]
                                        max_finish_rate = row[max_finish_rate_column_index - 1]
                                        weight = row[weight_column_index - 1]
                                        messurement = row[Measurement_column_index - 1]

                                        # save data to kpi user time period table
                                        user_year = KpiUserYear.objects.select_related('user','user__profile','kpi_year').filter(user_id=user_id_decoded,kpi_year_id=time_period.kpi_year).last()
                                        if user_year != None:
                                            user_time_period = KpiUserTimePeriod.objects.select_related('kpi_time_period','kpi_user_year').filter(kpi_time_period=time_period,kpi_user_year=user_year)
                                            if not user_time_period:
                                                user_time_period = KpiUserTimePeriod.objects.create(kpi_time_period=time_period,kpi_user_year=user_year,kpi_approval_status='pending')

                                            if goal_value is not None:
                                                goal_id = base64.b64decode(goal_value.encode()).decode()
                                                remarks_value_str = None

                                                # saving to kpi approve table
                                                if not KPIApprovalStatus.objects.select_related('approved_rejected_user','kpiuser_timeperiod').filter(approved_rejected_user_id = user_id_decoded, kpiuser_timeperiod=user_time_period.last(),action_type=KPI):
                                                    kpi_assignee = KPILevelPermissions.objects.select_related('user_id').filter(user_id_id = user_id_decoded,is_active=True,action_type=KPI)
                                                    for kpi_usr in kpi_assignee:
                                                        kpi_aprv_data = KPIApprovalStatus.objects.update_or_create(approved_rejected_user_id = user_id_decoded, added_by = kpi_usr.assigned_to, kpilevel_id = kpi_usr.kpilevel_id, is_approve=False, is_active=False,action_type=KPI, kpiuser_timeperiod=user_time_period.last())


                                                usertimeprd = KpiUserTimePeriod.objects.get(kpi_time_period=time_period,kpi_user_year=user_year)
                                                try:
                                                    if actual_value is not None and actual_value != '':
                                                        actual_value_str = str(actual_value)
                                                        actual_value_int = round(float(actual_value), 2)
                                                    else:
                                                        actual_value_str = ''
                                                        actual_value_int = None  # Or some other default value
                                                except:
                                                    actual_value_str = ''
                                                    actual_value_int = None
                                                try:
                                                    if target_value is not None and target_value != '':
                                                        target_value_str = str(target_value)
                                                        target_value_int = round(float(target_value), 2)
                                                    else:
                                                        target_value_str = ''
                                                        target_value_int = None  # Or some other default value
                                                except:
                                                    target_value_str = ''
                                                    target_value_int = None

                                                if actual_value_int == None or target_value_int == None:
                                                    if remarks_value is not None:
                                                        remarks_value_str = str(remarks_value)
                                                    KPIUserGoalValues.objects.update_or_create(kpiuser_goal_id=goal_id,goal_value=goalValue,kpiuser_timeperiod=usertimeprd,
                                                        defaults={
                                                            'max_finish_rate': max_finish_rate,
                                                            'weight': weight,
                                                            'actual': actual_value_int,
                                                            'target': target_value_int,
                                                            'remark': remarks_value_str
                                                        }
                                                    )
                                                elif actual_value_str == ''  and target_value_str == '':
                                                    KPIUserGoalValues.objects.update_or_create(
                                                            kpiuser_goal_id=goal_id,
                                                            goal_value=goalValue,
                                                            kpiuser_timeperiod=usertimeprd,
                                                            max_finish_rate=max_finish_rate,
                                                            weight=weight,
                                                            actual=None,
                                                            target=None,
                                                            remark=remarks_value_str
                                                        )
                                                if numeric_pattern.match(actual_value_str) and numeric_pattern.match(target_value_str):
                                                    # if remarks_value is not None:
                                                    #     remarks_value_str = str(remarks_value)
                                                    
                                                    # kpi_user_goal_value = KPIUserGoalValues.objects.select_related('kpiuser_goal','kpiuser_timeperiod').filter(kpiuser_goal_id = goal_id,kpiuser_timeperiod = usertimeprd)
                                                    # if kpi_user_goal_value:
                                                    #     kpi_user_goal_value.update(kpiuser_goal_id = goal_id, goal_value = goalValue, kpiuser_timeperiod = usertimeprd,max_finish_rate = max_finish_rate, weight = weight,actual = actual_value_str,target = target_value_str, remark = remarks_value_str)
                                                    # else:
                                                    #     KPIUserGoalValues.objects.create(kpiuser_goal_id = goal_id, goal_value = goalValue, kpiuser_timeperiod = usertimeprd,max_finish_rate = max_finish_rate, weight = weight,actual = actual_value_str,target = target_value_str, remark = remarks_value_str)

                                                    # save total score to user time period table
                                                    actual = actual_value_int
                                                    target = target_value_int
                                                    calculation = MeasurementType.objects.filter(name=messurement).last().calculation
                                                    if actual != 0 and target != 0:
                                                        if calculation == None or not calculation:
                                                            achieved = (actual / target) * 100
                                                        else:
                                                            achieved = eval(calculation)

                                                        if achieved >= max_finish_rate:
                                                            achieved = max_finish_rate
                                                        total_weightage += (achieved * weight) / 100
                                                    elif actual == 0 and target == 0:
                                                        achieved = 100
                                                        total_weightage += (achieved * weight) / 100
                                                    else:
                                                        pass

                                                    # if actual:
                                                    #     if remarks_value is not None:
                                                    #         remarks_value_str = str(remarks_value)
                                                    #     KpiUserActual.objects.update_or_create(added_by=request.user,kpi_user_time_period=user_time_period[0],kpi_user_goal_id=goal_id,defaults={'actual': actual,'remark': remarks_value_str})
                                                    # else:
                                                    #     pass

                                                    # if target:
                                                    #     # KpiUserTarget.objects.update_or_create(added_by=request.user,kpi_user_time_period=user_time_period[0],kpi_user_goal_id=goal_id,target=target)
                                                    #     KpiUserTarget.objects.update_or_create(added_by=request.user,kpi_user_time_period=user_time_period[0],kpi_user_goal_id=goal_id,defaults={'target': target})
                                                    # else:
                                                    #     pass
                                                    
                                                    if remarks_value is not None:
                                                        remarks_value_str = str(remarks_value)
                                                    else:
                                                        remarks_value_str = None
                                                    kpi_actual = KpiUserActual.objects.filter(added_by=request.user,kpi_user_time_period=user_time_period[0],kpi_user_goal_id=goal_id).last()

                                                    if kpi_actual:
                                                        kpi_actual.actual = actual
                                                        kpi_actual.remark = remarks_value_str
                                                        kpi_actual.save()
                                                    else:
                                                        KpiUserActual.objects.create(added_by=request.user,kpi_user_time_period=user_time_period[0],kpi_user_goal_id=goal_id,actual=actual,remark=remarks_value_str)

                                                    kpi_target = KpiUserTarget.objects.filter(added_by=request.user,kpi_user_time_period=user_time_period[0],kpi_user_goal_id=goal_id).last()

                                                    if kpi_target:
                                                        kpi_target.target = target
                                                        kpi_target.save()
                                                    else:
                                                        KpiUserTarget.objects.create(added_by=request.user,kpi_user_time_period=user_time_period[0],kpi_user_goal_id=goal_id,target=target)

                                                        
                                                    kpi_user_goal_value = KPIUserGoalValues.objects.select_related('kpiuser_goal','kpiuser_timeperiod').filter(kpiuser_goal_id = goal_id,kpiuser_timeperiod = usertimeprd)
                                                    if kpi_user_goal_value:
                                                        kpi_user_goal_value.update(kpiuser_goal_id = goal_id, goal_value = goalValue, kpiuser_timeperiod = usertimeprd,max_finish_rate = max_finish_rate, weight = weight,actual = actual,target = target, remark = remarks_value_str)
                                                    else:
                                                        KPIUserGoalValues.objects.create(kpiuser_goal_id = goal_id, goal_value = goalValue, kpiuser_timeperiod = usertimeprd,max_finish_rate = max_finish_rate, weight = weight,actual = actual,target = target, remark = remarks_value_str)
                                            

                                    if total_weightage:
                                        user_time_prd = KpiUserTimePeriod.objects.filter(kpi_time_period = time_period, kpi_user_year__user_id= user_id_decoded)         
                                        if user_time_prd:
                                            if user_time_prd and user_time_period.last().kpi_approval_status=='rejected':
                                                user_time_prd.update(score = total_weightage,kpi_approval_status = 'pending')
                                            user_time_prd.update(score = total_weightage) 
                                    else:
                                        KpiUserTimePeriod.objects.filter(kpi_time_period = time_period, kpi_user_year__user_id= user_id_decoded).update(score=0)
                        
                        # send notification to first level user
                        this_user = User.objects.only('first_name','last_name').get(id=user_id_decoded)
                        if KpiUserTimePeriod.objects.filter(kpi_time_period = time_period, kpi_user_year__user_id= user_id_decoded).exclude(kpi_approval_status__in=['approved', 'inprogress']).exists():
                            scheme = "https://" if request.is_secure() else "http://"
                            current_site = get_current_site(request)
                            domain_link = scheme + current_site.domain
                            # Construct the KPI approval detail link
                            approval_link = f"{domain_link}/kpi-approve-detail-view/{encrypt_me(user_id_decoded)}/{encrypt_me(time_period.id)}"

                            # Message for notification and email
                            msg = f"{this_user.first_name} {this_user.last_name}'s KPI values have been updated. Please start the KPI approval process."

                            kpi_aproval_qryset = KPIApprovalStatus.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','approved_rejected_user','added_by','kpilevel_id')
                            kpi_apprl_user = kpi_aproval_qryset.filter(approved_rejected_user_id=user_id_decoded,action_type=KPI,kpiuser_timeperiod__kpi_time_period=time_period).order_by('kpilevel_id')
                            if kpi_apprl_user:
                                kpi_apprl_user = kpi_apprl_user.first()
                                notification_info = {
                                    'type': 'kpi_sheet_upload',
                                    'action': 'Create',
                                    'action_id': int(time_period.id),
                                    'url': approval_link,
                                    'end_user': this_user.id,
                                }

                                # Send notification
                                notificationUpdate(user_from=request.user,user_to=kpi_apprl_user.added_by,message=msg,info=notification_info)

                                # Send email
                                mail_subject = 'KPI Goal Update Notification'
                                content_replace = {
                                    "NAME": f"{this_user.first_name} {this_user.last_name}",
                                    "MESSAGE": msg,
                                    "LINK": approval_link,
                                    "TITLE": "KPI Goal Update",
                                    "YEAR": datetime_timezone.now().year
                                }
                                send_email_to_users.delay(mail_subject, 4, content_replace, kpi_apprl_user.added_by.email)
                        # save to activity log and export log                     
                        info = {}
                        info['employees'] = list(employee_ids)
                        user_name = request.user.first_name +' '+ request.user.last_name
                        message = f"{user_name} uploaded a file: '{uploaded_file.name}' on {timezone.now()} in {time_period.name}."
                        log_data = {}
                        log_data['module_name'] = 'upload_kpi'
                        log_data['action_type'] = CREATE
                        log_data['log_message'] = message
                        log_data['status'] = SUCCESS
                        log_data['model_object'] = None
                        log_data['db_data'] = info
                        log_data['app_visibility'] = False
                        log_data['web_visibility'] = False
                        log_data['error_msg'] = None
                        log_data['fwd_link'] = ''
                        log_data['activity_mode'] = 'Web'
                        LogUserActivity(request, log_data)
                        info['error_msg'] = message
                        KPIExportLog.objects.create(file_name=uploaded_file.name,actor=request.user,kpi_timeperiod=time_period,action_type=EXPORT,info=info)
                        return JsonResponse({'result': 'success'})
            except Exception as e:
                # save to activity log and export log    
                print("-----------",str(e))
                info = {}
                info['employees'] = list(employee_ids)
                log_data = {}
                log_data['module_name'] = 'upload_kpi'
                log_data['action_type'] = CREATE
                log_data['log_message'] = None
                log_data['status'] = FAILED
                log_data['model_object'] = None
                log_data['db_data'] = info
                log_data['app_visibility'] = False
                log_data['web_visibility'] = False
                log_data['error_msg'] = traceback.format_exc()
                log_data['fwd_link'] = ''
                log_data['activity_mode'] = 'Web'
                LogUserActivity(request, log_data)
                info['error_msg'] = str(e)
                KPIExportLog.objects.create(file_name=uploaded_file.name,actor=request.user,kpi_timeperiod=time_period,action_type=EXPORT,info=info,status=FAILED)
                return JsonResponse({'result': 'failure','message': info['error_msg']})

        else:
            # save to activity log and export log 
            info = {}
            info['employees'] = list(employee_ids)
            info['error_msg'] = 'No file was uploaded'
            log_data = {}
            log_data['module_name'] = 'upload_kpi'
            log_data['action_type'] = CREATE
            log_data['log_message'] = None
            log_data['status'] = FAILED
            log_data['model_object'] = None
            log_data['db_data'] = info['employees']
            log_data['app_visibility'] = False
            log_data['web_visibility'] = False
            log_data['error_msg'] = traceback.format_exc()
            log_data['fwd_link'] = ''
            log_data['activity_mode'] = 'Web'
            LogUserActivity(request, log_data)
            KPIExportLog.objects.create(file_name=uploaded_file.name,actor=request.user,kpi_timeperiod=None,action_type=EXPORT,info=info,status=FAILED)
            return JsonResponse({'result': 'failure','message': info['error_msg'],'returnmesssage':info['error_msg']})
        
# Role access - permission to create levels
class KPIApprovalLevels(LoginRequiredMixin,View):
    login_url = '/'   

    def get(self,request,*args, **kwargs):
        context = {}
        page = request.GET.get('page', 1)
        context['kpi_levels'] = KPILevels.objects.filter(is_active=True,action_key=KPI).order_by('order')
        user_data = User.objects.select_related('profile', 'profile__department', 'profile__designation').\
            prefetch_related(Prefetch("kpilevel_user", queryset=KPILevelPermissions.objects.select_related('user_id', 'kpilevel_id', 'assigned_to').\
            filter(is_active=True,action_type=KPI).order_by('id'), to_attr='kpi_perms')).  filter(is_active=True, profile__employement_type = 2).exclude(is_superuser=True).order_by(Lower('first_name'))
        paginator = Paginator(user_data, PAGINATION_PERPAGE)
        try:
            user_data = paginator.page(page)
        except PageNotAnInteger:
            user_data = paginator.page(1)
        except EmptyPage:
            user_data = paginator.page(paginator.num_pages)
        if type(page) != int:
            page = int(page)

        context['user_data'] = user_data
        context['current_page'] = page
        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
        return renderfile(request, 'kpi', 'levels', 'kpi_level', context)
    
class EmployeeKPILevelFilter(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpilevels'

    def get(self, request, *args, **kwargs):
        try:
            page = request.GET.get('page', 1)
            data = {}
            context = {}
            conditions = {}
            conditions['is_active'] = True
            name = request.GET.get('name', None)
            department = request.GET.get('department', None)
            designation = request.GET.get('designation', None)
            if department:
                conditions['profile__department_id'] = department
            if designation:
                conditions['profile__designation_id'] = designation

            user_data = User.objects.select_related('profile', 'profile__department', 'profile__designation').\
                prefetch_related(Prefetch("kpilevel_user", queryset=KPILevelPermissions.objects.select_related('user_id', 'kpilevel_id', 'assigned_to').\
                filter(is_active=True,action_type=KPI).order_by('id'), to_attr='kpi_perms')).  filter(is_active=True, profile__employement_type = 2).exclude(is_superuser=True).order_by('first_name').\
                filter(**conditions).exclude(is_superuser=True).annotate(usersname=Concat('first_name', V(' '), 'last_name')).order_by(Lower('first_name'))
            if name:
                user_data = user_data.filter(usersname__icontains=name)

            paginator = Paginator(user_data, PAGINATION_PERPAGE)
            try:
                user_data = paginator.page(page)
            except PageNotAnInteger:
                user_data = paginator.page(1)
            except EmptyPage:
                user_data = paginator.page(paginator.num_pages)
            if type(page) != int:
                page = int(page)
            context['user_data'] = user_data
            context['current_page'] = page
            context['kpi_levels'] = KPILevels.objects.filter(is_active=True,action_key=KPI).order_by('order')
            template = render_to_string('hisensehr/kpi/levels/kpi_level_ajax.html', context=context, request=request)
            data['pagination'] = render_to_string("hisensehr/kpi/levels/kpi_level_pagination.html", context=context,
                                                  request=request)
            data['template'] = template
            data['status'] = True
        except Exception as e:
            print("......", e)
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class EmployeeKPILevelForm(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.add_kpilevels'

    def get(self, request, *args, **kwargs):
        data = {}
        user_id = request.GET.get('user_id', None)
        if user_id:
            user = User.objects.select_related('profile').\
                prefetch_related(Prefetch("kpilevel_user", queryset=KPILevelPermissions.objects.select_related('assigned_to').\
                filter( is_active=True,action_type=KPI).order_by('kpilevel_id'), to_attr='kpi_perms')).get(id=user_id)
        employees = User.objects.filter(is_active=True, profile__employement_type = 2).exclude(Q(id=user_id) | Q(is_superuser=True)).order_by(
            'first_name')
        kpi_levels = KPILevels.objects.filter(is_active=True,action_key=KPI).order_by('id')
        kpi_assigne = KpiActionPermission.objects.filter(user_id=user_id, kpi_action__in=[5, 6]).last()
        if kpi_assigne:
            kpi_assigne = kpi_assigne.assigned_to
                
        context = {'user': user, 'employees': employees, 'kpi_levels': kpi_levels, 'kpi_assigne':kpi_assigne}
        data['status'] = True
        data['template'] = render_to_string('hisensehr/kpi/levels/level_form.html', context, request=request)
        return JsonResponse(data)

    def post(self, request, *args, **kwargs):
        data = {}
        levels = []
        levelIds = []
        db = None
        user_id = request.POST.get('user_id', None)
        kpi_user = User.objects.get(id=int(user_id))
        
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  

        # checking kpi-permission is set or not
        checklist = ['kpi_settings','kpi_goal','goal_settings','guideline_settings','target_settings','actual_settings']
        kpi_permission = KpiActionPermission.objects.select_related('user','kpi_action').filter(user_id=user_id, kpi_action__key_action__in=checklist)  
        if not kpi_permission:
            message = f"Please grant KPI permissions to {kpi_user.first_name+ ' ' +kpi_user.last_name} before configuring the KPI levels."
            data['message'] = message
            data['status'] = False
            return JsonResponse(data)

        # for levels
        kpi_level = KPILevels.objects.filter(action_key='kpi').order_by('order')
        for kpi_lvl in kpi_level:
            stage_lvl = request.POST.get(kpi_lvl.name, 0)
            if not stage_lvl:
                stage_lvl = str(kpi_lvl.id)+'$$0'
            sel_level_id, sel_usr = stage_lvl.split('$$')
            levels.append(int(sel_usr))
            levelIds.append(int(sel_level_id))
        

        non_zero_levels = [item for item in levels if item != 0]
        duplicates = len(non_zero_levels) != len(set(non_zero_levels))
        level_result_dict = dict(zip(levelIds, levels))
        if duplicates:
            data['duplicates'] = True
            return JsonResponse(data)

        is_active_users = User.objects.filter(id__in=non_zero_levels,is_active=True)
        if len(non_zero_levels) != is_active_users.count():
            permission_flag = 0
            not_active_users = User.objects.filter(id__in=non_zero_levels,is_active=False) 
            inactive_users_name =  [usr.first_name+ ' ' +usr.last_name for usr in not_active_users]
            inactive_users_id =  [usr.id for usr in not_active_users]
            if level_result_dict.get(2) in inactive_users_id:
                permission_flag = 1

            data['status'] = False
            if len(inactive_users_name) == 1:
                message = f"{inactive_users_name[0]} is inactive. Levels can't be updated."
            else:
                message = f"{', '.join(inactive_users_name)} are inactive. Levels can't be updated."
            if permission_flag == 1:
                message+=', change goal permission.'
            
            data['message'] = message
            return JsonResponse(data)
        
        else:
            try:
                with transaction.atomic():
                    kpi_user_timeperiod = KpiUserTimePeriod.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_time_period','kpi_time_period__kpi_year').filter(kpi_user_year__user_id=user_id,kpi_time_period__kpi_year__current_year = True,kpi_time_period__kpi_year__is_active = True)
                    kpi_timeperiod = kpi_user_timeperiod.last()
                    if kpi_timeperiod and kpi_timeperiod.kpi_approval_status == 'inprogress':
                        data['status'] = False
                        data['message'] = 'Workflow cannot be modified after initiating the approval'
                        return JsonResponse(data)
                    kpi_approval_status = KPIApprovalStatus.objects.select_related('added_by').filter(approved_rejected_user=user_id)
                    dynamic_menu_id = KPI_APPROVE_MENU
                    # Delete the old KPILevelPermissions records
                    # KPILevelPermissions.objects.filter(user_id=user_id).delete()

                    from_user = request.user.first_name + " " + request.user.last_name
                    get_user = get_object_or_404(User, id=user_id)
                    to_user = get_user.first_name + " " + get_user.last_name
                    log_msg = f"kpi approval levels of user {to_user} has been updated."
                    log_msg_2 = f"{from_user} updated kpi approval levels for user {to_user}"
                
                    current_levels = KPILevelPermissions.objects.filter(user_id=user_id,is_active=True,action_type=KPI).order_by('id')
                    asssigned_users = [i.assigned_to_id for i in current_levels]
                    updated_users = [int(i) for i in levels if asssigned_users.count(int(i)) == 0 and i != 0]
                    # removing last user directly by selecting please select a new employee getting removed users
                    non_matching_values = []
                    # Check for matching values
                    for user in asssigned_users:
                        if user not in levels:
                            non_matching_values.append(user)
                    if current_levels:
                        if updated_users:
                            current_usr_dlt = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=False,action_type=KPI).delete()
                            level_ar = []
                            for level in current_levels:
                                level_assigned = level.assigned_to
                                if str(level_assigned.id) not in levels:
                                    level.is_active = False
                                    level.save()
                                    level_ar.append(level.kpilevel_id)
                           
                            reverse_dict = {v: k for k, v in level_result_dict.items()}
                            edited_levls = []
                            for usr in updated_users:
                                lvlId = reverse_dict.get(usr)
                                edited_levls.append(lvlId)
                                userid = User.objects.get(id=usr)
                                # add user data updated to KPIApprovalStatus
                                apprval_chng = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpilevel_id').filter(Q(approved_rejected_user=user_id,kpilevel_id=lvlId,is_approve=False,approval_status=PENDING,action_type=KPI)|Q(approved_rejected_user=user_id,kpilevel_id=lvlId,is_approve=False,approval_status=REJECTED,action_type=KPI))
                                if apprval_chng:
                                    db = KPILevelPermissions.objects.create(user_id_id = user_id,kpilevel_id_id = lvlId,assigned_to = userid,action_type=KPI)
                                    apprvl_status_chng = apprval_chng.update(added_by=userid)       
                                else:
                                    db = KPILevelPermissions.objects.create(user_id_id = user_id,kpilevel_id_id = lvlId,assigned_to = userid,action_type=KPI)
                                    if kpi_approval_status:
                                        try:
                                            kpi_user_timeprd = KpiUserTimePeriod.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_user_year__kpi_year').get(kpi_user_year__user_id=user_id, kpi_user_year__kpi_year__current_year=True, kpi_user_year__kpi_year__is_active=True)
                                            old_data_exists = KPIApprovalStatus.objects.select_related('approved_rejected_user', 'kpilevel_id','kpi_user_year','kpi_user_year__kpi_year').filter(
                                                Q(approved_rejected_user=user_id, kpilevel_id=lvlId, is_active=True, action_type=KPI),
                                                kpi_user_year__kpi_year__current_year=True
                                            ).exists()
                                            
                                            if not old_data_exists and kpi_user_timeprd.kpi_approval_status != 'approved':
                                                KPIApprovalStatus.objects.create(
                                                    added_by=userid,
                                                    approved_rejected_user_id=user_id,
                                                    kpilevel_id_id=lvlId,
                                                    is_approve=False,
                                                    is_active=False,
                                                    approval_status=PENDING,
                                                    kpiuser_timeperiod_id=kpi_user_timeprd.id,
                                                    action_type=KPI
                                                )
                                        except KpiUserTimePeriod.DoesNotExist:
                                            pass
                                        # user_time_periods = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpilevel_id').filter(Q(approved_rejected_user=user_id,action_type=KPI)).values('kpiuser_timeperiod').distinct()
                                        # for usr_timeperiod in user_time_periods:
                                        #     if kpi_timeperiod.last().kpi_approval_status != 'approved':
                                        #         KPIApprovalStatus.objects.create(added_by=userid,approved_rejected_user_id=user_id,kpilevel_id_id=lvlId,is_approve=False,is_active=False,approval_status=PENDING,kpiuser_timeperiod_id=usr_timeperiod['kpiuser_timeperiod'],action_type=KPI)
                                if dynamic_menu_id: 
                                    # User_menu_access.objects.update_or_create(user_id=userid.id,  dynamic_menu_id = REQUESTS_KPI_MAIN_MENU, defaults={'dynamic_menu_id': REQUESTS_KPI_MAIN_MENU})
                                    # User_menu_access.objects.update_or_create(user_id=userid.id, dynamic_menu_id = dynamic_menu_id, defaults={'dynamic_menu_id': dynamic_menu_id})
                                    user_menu_access_queryset =  User_menu_access.objects.select_related('user','dynamic_menu')
                                    
                                    user_menu_access = user_menu_access_queryset.filter(user_id=userid.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = PERFORMANCE_CONFIG_MENU
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=userid.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU)

                                    user_menu_access = user_menu_access_queryset.filter(user_id=userid.id, dynamic_menu_id=dynamic_menu_id).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = dynamic_menu_id
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=userid.id, dynamic_menu_id=dynamic_menu_id)
                            
                                # sending notification and mail to the selected users
                                lvl_usr = User.objects.get(id=int(usr))
                                link = domainlink+'/kpi-approve-view/'
                                info = {}
                                info['type'] = 'kpi_emp'
                                info['action'] = 'Create'
                                info['action_id'] = db.id
                                info['user_id'] = user_id
                                info['url'] = link
                                # info['kpi_time_period_id'] = qtr_id
                                info['end_user'] = request.user.pk
                                msg = f"You have been added to {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                notificationUpdate(user_from=request.user,user_to=userid,message=msg, info=info)

                                #email notification                    
                                mail_subject = 'KPI Approval Updates'
                                user_name = userid.first_name+" "+userid.last_name
                                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, userid.email)

                                # notification and mail for removed user   
                                removed_usr = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,kpilevel_id_id = lvlId,is_active=False,action_type=KPI).last()
                                if removed_usr:
                                    msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                    notificationUpdate(user_from=request.user,user_to=removed_usr.assigned_to,message=msg, info=info)

                                    #email notification                    
                                    mail_subject = 'KPI Approval Updates'
                                    user_name = userid.first_name+" "+userid.last_name
                                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                    send_email_to_users.delay(mail_subject, 4, content_replace, removed_usr.assigned_to.email)
                                

                            KPILevelPermissions.objects.select_related('user_id','kpilevel_id').filter(user_id_id = user_id,action_type=KPI).exclude(kpilevel_id_id__in = edited_levls).update(is_active = True)
                            flush_cache('cached_menu_user') 
                                            
                            log_data = {}
                            log_data['module_name'] = 'employee_kpi_approval_levels_updated'
                            log_data['action_type'] = UPDATE
                            log_data['log_message'] = log_msg
                            log_data['status'] = SUCCESS
                            log_data['model_object'] = db
                            log_data['db_data'] = {'id': updated_users, 'user': from_user, 'action': log_msg_2}
                            log_data['app_visibility'] = False
                            log_data['web_visibility'] = True
                            log_data['error_msg'] = ''
                            log_data['fwd_link'] = '/kpi_approval_levels/'
                            LogUserActivity(request, log_data)
                            # data['status'] = True
                            # return JsonResponse(data)

                        # removing last user directly by selecting please select a new employee
                        if non_matching_values:
                            for re_user in non_matching_values:
                                rem_user = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,assigned_to_id=re_user,is_active=True,action_type=KPI).last()
                                link = domainlink+'/kpi-approve-view/'
                                info = {}
                                info['type'] = 'kpi_emp'
                                info['action'] = 'Create'
                                info['action_id'] = 0
                                info['user_id'] = user_id
                                info['url'] = link
                                # info['kpi_time_period_id'] = qtr_id
                                info['end_user'] = request.user.pk  
                                if rem_user:
                                    # deleting kpi workflow for updating with new one
                                    KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,assigned_to_id=re_user,is_active=True,action_type=KPI).order_by('id').delete()

                                    # remove the user from the approval status table if it is not approved
                                    approval_status = KPIApprovalStatus.objects.select_related('approved_rejected_user').filter(approved_rejected_user_id=user_id, added_by_id=re_user, action_type=KPI).last()
                                    if approval_status and approval_status.approval_status == 'pending':
                                        approval_status.delete()

                                    # indimating the kpi workflow users that they are removed
                                    msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                    notificationUpdate(user_from=request.user,user_to=rem_user.assigned_to,message=msg, info=info)
                                    #email notification                    
                                    mail_subject = 'KPI Approval Updates'
                                    user_name = rem_user.assigned_to.first_name+" "+rem_user.assigned_to.last_name
                                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                    send_email_to_users.delay(mail_subject, 4, content_replace, rem_user.assigned_to.email)
                                    if dynamic_menu_id:
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=rem_user.assigned_to.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).delete()
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=rem_user.assigned_to.id, dynamic_menu_id=dynamic_menu_id).delete()

                            data['status'] = True
                            data['message'] = 'KPI approval levels updated successfully'
                            return JsonResponse(data)
                        data['status'] = True
                        return JsonResponse(data)
                    
                    else:
                        users = []
                        for level, user in level_result_dict.items():
                            if int(user) != 0 :
                                db = KPILevelPermissions()
                                db.user_id_id = user_id
                                db.kpilevel_id_id = level
                                db.assigned_to_id = int(user)
                                db.action_type = KPI
                                db.save()
                                # level_set = set(int(level) for level in levels if int(level) != 0)
                                # User_menu_access.objects.exclude(user_id__in=level_set).filter(dynamic_menu_id=dynamic_menu_id).delete()
                                flush_cache('cached_menu_user')

                                if dynamic_menu_id:  
                                    # User_menu_access.objects.update_or_create(user_id=level,  dynamic_menu_id = REQUESTS_KPI_MAIN_MENU, defaults={'dynamic_menu_id': REQUESTS_KPI_MAIN_MENU})
                                    # User_menu_access.objects.update_or_create(user_id=level, dynamic_menu_id = dynamic_menu_id, defaults={'dynamic_menu_id': dynamic_menu_id})
                                    user_menu_access_queryset =  User_menu_access.objects.select_related('user','dynamic_menu')
                                    
                                    user_menu_access =user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = PERFORMANCE_CONFIG_MENU
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU)

                                    user_menu_access = user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=dynamic_menu_id).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = dynamic_menu_id
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=user, dynamic_menu_id=dynamic_menu_id)


                                    flush_cache('cached_menu_user')
                                users.append(db.id)

                                # sending notification and mail to the selected users
                                lvl_usr = User.objects.get(id=int(user))
                                link = domainlink+'/kpi-approve-view/'
                                info = {}
                                info['type'] = 'kpi_emp'
                                info['action'] = 'Create'
                                info['action_id'] = db.id
                                info['user_id'] = user_id
                                info['url'] = link
                                # info['kpi_time_period_id'] = qtr_id
                                info['end_user'] = request.user.pk
                                msg = f"You have been added to {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                notificationUpdate(user_from=request.user,user_to=lvl_usr,message=msg, info=info)

                                #email notification                    
                                mail_subject = 'KPI Approval Updates'
                                user_name = lvl_usr.first_name+" "+lvl_usr.last_name
                                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, lvl_usr.email)

                        if db == None:
                            data['status'] = False
                            data['message'] = 'Please select at least one level for an employee before saving.'
                            return JsonResponse(data)
                        log_data = {}
                        log_data['module_name'] = 'employee_kpi_approval_levels'
                        log_data['action_type'] = UPDATE
                        log_data['log_message'] = log_msg
                        log_data['status'] = SUCCESS
                        log_data['model_object'] = db
                        log_data['db_data'] = {'id': users, 'user': from_user, 'action': log_msg_2}
                        log_data['app_visibility'] = False
                        log_data['web_visibility'] = True
                        log_data['error_msg'] = ''
                        log_data['fwd_link'] = '/kpi_approval_levels/'
                        LogUserActivity(request, log_data)
                        data['status'] = True
                        return JsonResponse(data)
            except Exception as dberror:
                print('--')
                print(dberror)
                log_data = {}
                log_data['module_name'] = 'employee_kpi_approval_levels'
                log_data['action_type'] = CREATE
                log_data['log_message'] = 'employee kpi approval level updation failed'
                log_data['status'] = FAILED
                log_data['model_object'] = None
                log_data['db_data'] = {}
                log_data['app_visibility'] = False
                log_data['web_visibility'] = False
                log_data['error_msg'] = traceback.format_exc()
                log_data['fwd_link'] = '/kpi_approval_levels/'
                LogUserActivity(request, log_data)

                data['status'] = False
                data['message'] = "Something went wrong"
                return JsonResponse(data)

class EmployeeKPILevelDeleteView(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.delete_kpiyear'

    @transaction.atomic
    def get(self, request, *args, **kwargs):
        data = {}
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  
        try:
            user_id = kwargs['id']
            dynamic_menu_id = KPI_APPROVE_MENU
            kpi_data = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,action_type=KPI)
            kpi_timeperiod = KpiUserTimePeriod.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_user_year__kpi_year').filter(kpi_user_year__user_id=user_id,kpi_user_year__kpi_year__current_year=True)

            if kpi_data:
                if kpi_timeperiod.exists():
                    user_timeperiod_status = kpi_timeperiod.last().kpi_approval_status
                    if user_timeperiod_status == 'inprogress':
                        data['status'] = False
                        data['message'] = "Workflow cannot be modified after initiating the approval"
                        return JsonResponse(data)
                    
                KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,action_type=KPI).delete()
                
                # remove the user from the approval status table if it is not approved in kpi
                if kpi_timeperiod.exists():
                    if kpi_timeperiod.last().kpi_approval_status not in ['inprogress','approved']:
                        approval_status = KPIApprovalStatus.objects.select_related('approved_rejected_user').filter(approved_rejected_user_id=user_id, action_type=KPI,kpiuser_timeperiod__kpi_user_year__kpi_year__current_year=True)
                        if approval_status:
                            approval_status.delete()
                
                usr_menu = User_menu_access.objects.select_related('user_id','dynamic_menu').exclude(user_id=user_id).filter(dynamic_menu_id=dynamic_menu_id)
                if usr_menu.count() == 0:
                    usr_menu.delete()
                from_user = request.user.first_name + " " + request.user.last_name
                get_user = get_object_or_404(User, id=user_id)
                to_user = get_user.first_name + " " + get_user.last_name
                log_msg = f"kpi permission levels of user {to_user} has been deleted."
                log_msg_2 = f"{from_user} deleted kpi permission levels of user {to_user}"
                data['status'] = True

                kpi_user = User.objects.get(id=int(user_id))
                for usr in kpi_data:
                    # sending notification and mail to the selected users
                    link = domainlink+'/kpi-approve-view/'
                    info = {}
                    info['type'] = 'kpi_emp'
                    info['action'] = 'Create'
                    info['action_id'] = user_id
                    info['user_id'] = user_id
                    info['url'] = link
                    info['end_user'] = request.user.pk
                    # notification and mail for removed user   
                    msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                    notificationUpdate(user_from=request.user,user_to=usr.assigned_to,message=msg, info=info)

                    #email notification                    
                    mail_subject = 'KPI Approval Updates'
                    user_name = kpi_user.first_name+" "+kpi_user.last_name
                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                    send_email_to_users.delay(mail_subject, 4, content_replace, usr.assigned_to.email)
                    

                log_data = {}
                log_data['module_name']  = 'kpi_approval_levels'
                log_data['action_type']  = DELETE
                log_data['log_message']  = log_msg
                log_data['status']  = SUCCESS
                log_data['model_object']  =''
                log_data['db_data']  = {'ids':'', 'user':from_user, 'action':log_msg_2}
                log_data['app_visibility']  = False
                log_data['web_visibility']  = True
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/kpi_approval_levels/'
                LogUserActivity(request, log_data)
        except Exception as dberror:
            log_data = {}
            log_data['module_name'] = 'kpi_approval_levels'
            log_data['action_type'] = DELETE
            log_data['log_message'] = 'employee kpi permission level deletion failed'
            log_data['status'] = FAILED
            log_data['model_object'] = None
            log_data['db_data'] = {}
            log_data['app_visibility'] = False
            log_data['web_visibility'] = False
            log_data['error_msg'] = traceback.format_exc()
            log_data['fwd_link'] = '/kpi_approval_levels/'
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)


class EmployeeKPIApproveView(LoginRequiredMixin, View):
    login_url = '/'

    def get(self, request, *args, **kwargs):
        context, data = {}, {}
        page = request.GET.get('page', 1)
        code = request.GET.get('code', None)

        kpi_user_timeperiod = KpiUserTimePeriod.objects.select_related(
            'kpi_user_year','kpi_user_year__kpi_year','kpi_user_year__user','kpi_user_year__user__profile',
            'kpi_time_period','kpi_time_period__kpi_year'
        ).prefetch_related(
            Prefetch('kpiuser_timeperiod',
                queryset=KPIApprovalStatus.objects.select_related(
                    'added_by','approved_rejected_user','approved_rejected_user__profile',
                    'approved_rejected_user__profile__department','approved_rejected_user__profile__designation',
                    'kpilevel_id'
                ).filter(added_by=request.user,action_type=KPI), 
                to_attr='kpi_approval')
        ).filter(
            kpi_time_period__kpi_year__current_year=True,
            kpi_time_period__kpi_year__is_active=True,
            kpi_approval_status__in=['pending','inprogress']
        ).order_by('kpi_user_year__user__first_name', 'kpi_user_year__user__last_name', '-id')
        
        kpi_approval_statuses_list = []
        kpi_apprv_distinct_usr = []
        year_quater = {}

        if code:
            kpi_user_timeperiod = kpi_user_timeperiod.filter(kpi_user_year__user__profile__employee_id=code)
        else:
            kpi_user_timeperiod = kpi_user_timeperiod

        for kpi_timeperiod in kpi_user_timeperiod:
            current_year = kpi_timeperiod.kpi_time_period.kpi_year.name
            quater = kpi_timeperiod.kpi_time_period.name
            if current_year+' '+quater not in  year_quater:
                year_quater[kpi_timeperiod.kpi_time_period.id]=current_year+' '+quater
            if kpi_timeperiod.kpi_approval:
                for kpi_approval_status in kpi_timeperiod.kpi_approval:
                    kpiuser_timeperiod_id = kpi_approval_status.kpiuser_timeperiod_id
                    kpi_apprvl_fltr = KPIApprovalStatus.objects.select_related(
                        'kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','approved_rejected_user','approved_rejected_user__profile',
                        'approved_rejected_user__profile__designation','approved_rejected_user__profile__department','kpilevel_id','added_by','added_by__profile'
                    ).filter(kpiuser_timeperiod__id=kpiuser_timeperiod_id,approved_rejected_user=kpi_timeperiod.kpi_user_year.user,action_type=KPI)
                    kpi_apprv_distinct_usr.append(kpi_apprvl_fltr.distinct('approved_rejected_user'))
                    kpi_approval_statuses_list.extend(kpi_apprvl_fltr)
        
        kpi_approval_statuses_list = sorted(kpi_approval_statuses_list, key=lambda x: x.kpilevel_id.id)
        distinct_users_list = [item for sublist in kpi_apprv_distinct_usr for item in sublist]
        year_quater = dict(sorted(year_quater.items()))
       
        paginator = Paginator(distinct_users_list, PAGINATION_PERPAGE)
        try:
            kpi_users = paginator.page(page)
        except PageNotAnInteger:
            kpi_users = paginator.page(1)
        except EmptyPage:
            kpi_users = paginator.page(paginator.num_pages)

        if not isinstance(page, int):
            page = int(page)

        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
        context['filter_quater'] = KpiTimePeriod.objects.select_related('kpi_year').filter(is_active=True,kpi_year__current_year=True)
        context['kpi_year'] = KpiYear.objects.filter(is_active=True).order_by('-start_date')
        context['kpi_approval_statuses_list'] = kpi_approval_statuses_list
        context['distinct_users_list'] = distinct_users_list
        context['year_quater'] = year_quater
        context['current_user'] = request.user

        return renderfile(request, 'kpi/levels', 'kpi_approve', context)


class EmployeeKPIApproveViewFilter(LoginRequiredMixin, View):
    login_url = '/'

    def get(self, request, *args, **kwargs):
        context, data = {}, {}
        search_key = request.GET.get('search_key', None)
        department = request.GET.get('department', None)
        designation = request.GET.get('designation', None)
        quater_filter = request.GET.get('quater', None)
        year = request.GET.get('year', None)
        status = request.GET.get('status', None)
        page = request.GET.get('page', 1)

        # Add missing relations to reduce duplicate queries
        kpi_user_timeperiod = KpiUserTimePeriod.objects.select_related(
            'kpi_user_year','kpi_user_year__kpi_year','kpi_user_year__user','kpi_user_year__user__profile',
            'kpi_time_period','kpi_time_period__kpi_year'
        ).prefetch_related(
            Prefetch('kpiuser_timeperiod',
                queryset=KPIApprovalStatus.objects.select_related(
                    'added_by','approved_rejected_user','approved_rejected_user__profile',
                    'approved_rejected_user__profile__department','approved_rejected_user__profile__designation',
                    'kpilevel_id'
                ).filter(added_by=request.user,action_type=KPI), 
                to_attr='kpi_approval')
        ).filter(
            kpi_time_period__kpi_year__is_active=True,
            kpi_time_period__kpi_year_id=year
        ).order_by('kpi_user_year__user__first_name', 'kpi_user_year__user__last_name', '-id')
        
        kpi_approval_statuses_list = []
        kpi_apprv_distinct_usr = []
        year_quater = {}
        
        for kpi_timeperiod in kpi_user_timeperiod:
            current_year = kpi_timeperiod.kpi_time_period.kpi_year.name
            quater = kpi_timeperiod.kpi_time_period.name
            if current_year+' '+quater not in year_quater:
                year_quater[kpi_timeperiod.kpi_time_period.id]=current_year+' '+quater
            if kpi_timeperiod.kpi_approval:
                for kpi_approval_status in kpi_timeperiod.kpi_approval:
                    kpiuser_timeperiod_id = kpi_approval_status.kpiuser_timeperiod_id
                    kpi_apprvl_fltr = KPIApprovalStatus.objects.select_related(
                        'kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','approved_rejected_user','approved_rejected_user__profile',
                        'approved_rejected_user__profile__designation','approved_rejected_user__profile__department',
                        'kpilevel_id','added_by','added_by__profile'
                    ).filter(kpiuser_timeperiod__id=kpiuser_timeperiod_id,approved_rejected_user=kpi_timeperiod.kpi_user_year.user,action_type=KPI)
                    kpi_apprv_distinct_usr.append(kpi_apprvl_fltr.distinct('approved_rejected_user'))
                    kpi_approval_statuses_list.extend(kpi_apprvl_fltr)
        
        kpi_approval_statuses_list = sorted(kpi_approval_statuses_list, key=lambda x: x.kpilevel_id.id)
        distinct_users_list = [item for sublist in kpi_apprv_distinct_usr for item in sublist]

        # Apply filters
        temp_distinct_users_list = distinct_users_list

        if search_key:
            search_words = search_key.lower().split()
            search_pattern = r'.*'.join(re.escape(word) for word in search_words)
            temp_distinct_users_list = [
                queryset for queryset in temp_distinct_users_list if (
                    re.search(search_pattern, queryset.approved_rejected_user.first_name.lower()) or
                    re.search(search_pattern, queryset.approved_rejected_user.last_name.lower()) or
                    re.search(search_pattern, queryset.approved_rejected_user.email.lower()) or
                    re.search(search_pattern, queryset.approved_rejected_user.profile.employee_id.lower()) or
                    re.search(search_pattern, (queryset.approved_rejected_user.first_name + ' ' + queryset.approved_rejected_user.last_name).lower())
                )
            ]

        if designation:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list
                                        if int(designation) == queryset.approved_rejected_user.profile.designation.id]

        if department:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list
                                        if int(department) == queryset.approved_rejected_user.profile.department.id]

        if quater_filter:
            quater_id = KpiTimePeriod.objects.filter(is_active=True,kpi_year_id=year,name=quater_filter).values('id')[0]['id']
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list
                                        if int(quater_id) == queryset.kpiuser_timeperiod.kpi_time_period.id]
            quater_data_list = [int(quater_id)] 
        else:
            quater_data_list = [item['id'] for item in KpiTimePeriod.objects.filter(is_active=True).values('id')]

        if year:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list
                                        if int(year) == queryset.kpiuser_timeperiod.kpi_user_year.kpi_year.id]
        
        if status:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list if status == queryset.kpiuser_timeperiod.kpi_approval_status]
        else:
            status = ['pending','inprogress']
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list if queryset.kpiuser_timeperiod.kpi_approval_status in status]

        distinct_users_list = temp_distinct_users_list
        year_quater = dict(sorted(year_quater.items()))

        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
        context['kpi_approval_statuses_list'] = kpi_approval_statuses_list
        context['distinct_users_list'] = distinct_users_list
        context['year_quater'] = year_quater
        context['quater_data_list'] = quater_data_list
        context['current_user'] = request.user

        template = render_to_string('hisensehr/kpi/levels/kpi_approve_ajax.html', context=context, request=request)
        context['filter_quater'] = KpiTimePeriod.objects.select_related('kpi_year').filter(kpi_year_id=year,is_active=True).order_by('id')
        context['sel_quater'] = quater_filter if quater_filter else 0
        template2 = render_to_string('hisensehr/kpi/levels/quaterfilter_ajax.html', context=context, request=request)

        data['template'] = template
        data['template2'] = template2
        data['status'] = True

        return JsonResponse(data)

class EmployeeKPIApproveDetailView(LoginRequiredMixin,View):
    login_url = '/'   

    def get(self, request, *args, **kwargs):
        context, condition, data = {}, {}, {}
        try:
            apprv_reject_id = decrypt_me(kwargs['kpi'])
            qtr_id = decrypt_me(kwargs['qtr'])
        except:
            raise Http404
        button_flag = 1
        reject_button_flag = 1
        previous_approved_rejected_flag = 0

        current_kpi_year = KpiYear.objects.get(current_year=True,is_active=True)
        kpi_usr_timprd = KpiUserTimePeriod.objects.select_related('kpi_time_period', 'kpi_user_year', 'kpi_user_year__user').prefetch_related('kpiuser_timeperiod')
        kpicurrent_user_timperiod = kpi_usr_timprd.filter(kpi_time_period=qtr_id)
        # kpiuser_timeperiod_ids = [item.id for item in kpi_usr_timprd]
        kpi_apprv_list = KPIApprovalStatus.objects.select_related(
            'kpiuser_timeperiod','approved_rejected_user','approved_rejected_user__profile','approved_rejected_user__profile__designation','approved_rejected_user__profile__department','added_by','kpilevel_id').\
                prefetch_related('kpi_approvalstatus')\
            .filter(kpiuser_timeperiod__id__in=kpicurrent_user_timperiod,approved_rejected_user=apprv_reject_id,action_type=KPI).order_by('kpilevel_id')
        kpi_current_user_level = kpi_apprv_list.filter(added_by = request.user).last()
        if kpi_current_user_level.is_active == True:
            context['approve_reject_status'] = kpi_current_user_level.approval_status

        if kpi_current_user_level.kpilevel_id_id == 2:
            previous_approved_rejected_flag = 1
        else:
            previous_level = kpi_apprv_list.filter(kpiuser_timeperiod__kpi_time_period_id=qtr_id,approved_rejected_user_id=apprv_reject_id,kpilevel_id_id=(kpi_current_user_level.kpilevel_id_id-1)).last()
            if previous_level.is_active == True:
                previous_approved_rejected_flag = 1
            
        kpi_apprv_list_prev_qtr = kpi_apprv_list.filter(is_active=False,is_approve=False,kpiuser_timeperiod__kpi_time_period_id__lt=qtr_id,approved_rejected_user_id=apprv_reject_id)
        if kpi_apprv_list_prev_qtr:
            button_flag=0

        if kpi_current_user_level.kpilevel_id_id == 2:
            reject_button_flag = 0 

        if button_flag == 1:
            kpi_same_qtr_prev_lvl = kpi_apprv_list.filter(is_approve=False,kpiuser_timeperiod__kpi_time_period_id=qtr_id,approved_rejected_user_id=apprv_reject_id,kpilevel_id__lt=kpi_current_user_level.kpilevel_id)
            if kpi_same_qtr_prev_lvl:
                button_flag = 0

        if button_flag == 1:
            kpi_same_qtr_nxt_lvl = kpi_apprv_list.filter(is_approve=True,kpiuser_timeperiod__kpi_time_period_id=qtr_id,approved_rejected_user_id=apprv_reject_id,kpilevel_id__gt=kpi_current_user_level.kpilevel_id)
            if kpi_same_qtr_nxt_lvl:
                button_flag = 0

        # filter remarks based on quater
        kpi_remarks_list = KPIRemarks.objects.select_related('approve_reject_user','kpi_added_by','kpi_approvalstatus__kpiuser_timeperiod').filter(approve_reject_user=apprv_reject_id,kpi_approvalstatus__kpiuser_timeperiod__in=kpicurrent_user_timperiod).order_by('id')

        # Check if updated_score is not None and get the modified log
        if kpi_apprv_list:
            modified_log = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=kpicurrent_user_timperiod.last())
            if modified_log:
                # group the modified log based on the value type(cricism and appreciation)
                criticism_list = [log for log in modified_log if log.value_type == 1]
                appreciation_list = [log for log in modified_log if log.value_type == 2]
                # sum up the values for both criticism and appreciation
                criticism_value_sum = sum(log.value for log in criticism_list)
                appreciation_value_sum = sum(log.value for log in appreciation_list)
                # pass thosee values to the html
                context['criticism_list'] = criticism_list
                context['appreciation_list'] = appreciation_list
                context['criticism_value_sum'] = criticism_value_sum
                context['appreciation_value_sum'] = appreciation_value_sum
            if modified_log:
                modified_log= modified_log.last()
        else:
            modified_log = None
        user_ids = [str(request.user.id)]
        receiver_conditions = Q()
        sender_conditions = Q()
        for user_id in user_ids:
            receiver_conditions |= Q(receiver__contains=str(user_id))
        sender_conditions = Q(sender = request.user)
        # Add the condition to check if user_id_to_check is in the receiver field
        receiver_conditions |= Q(receiver__contains=user_ids)
        query_conditions = Q(
            is_active=True,
            kpi_user_id=apprv_reject_id,
            user_timeperiod__kpi_time_period_id=qtr_id,
            action_type=1
        ) & receiver_conditions 
    
        sender_query_conditions = Q(
            is_active=True,
            kpi_user_id=apprv_reject_id,
            user_timeperiod__kpi_time_period_id=qtr_id,
            action_type=1
        ) & sender_conditions

        approval_messages = ApprovalMessages.objects.filter(Q(query_conditions)|Q(sender_query_conditions)).order_by('-id')

        for message in approval_messages:
            receiver_id_list = ast.literal_eval(message.receiver)
            receiver_id = [int(id) for id in receiver_id_list]
            if receiver_id:
                user_data = User.objects.filter(id__in=receiver_id).values('first_name', 'last_name')
                user_names = ', '.join([f"{user['first_name']} {user['last_name']}" for user in user_data])
                message.receiver = user_names
        goal_approval_status = KpiUserYear.objects.select_related('user','kpi_year').filter(user=apprv_reject_id,kpi_year=current_kpi_year).last()       
        context['kpi_current_user_level_status'] = kpi_current_user_level.is_approve
        context['goal_approval_status'] = goal_approval_status
        context['kpi_apprv_list'] = kpi_apprv_list
        context['current_user'] = kpi_apprv_list.last()
        context['user_goals'] = KPIUserGoalValues.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','kpiuser_goal','kpiuser_goal__kpi_user_year','kpiuser_goal__kpi_user_year__user','kpiuser_goal__kpi_goal__measurement_type','kpiuser_goal__kpi_goal__kpi_category')\
            .filter(kpiuser_timeperiod__kpi_time_period=qtr_id,kpiuser_goal__kpi_user_year__user=apprv_reject_id)
        context['disable_button'] = any(goal.actual is None or goal.target is None for goal in context['user_goals'])
        context['guideline'] = get_kpi_guideline(apprv_reject_id, current_kpi_year.id)
        context['button_flag'] = button_flag
        context['reject_button_flag'] = reject_button_flag
        context['qtr_id'] = qtr_id
        context['kpi_remarks_list'] = kpi_remarks_list
        context['current_appv'] = kpi_apprv_list.filter(added_by_id = request.user).last()
        context['approval_messages'] = approval_messages
        context['previous_level'] = previous_approved_rejected_flag
        context['modified_log'] = modified_log
        context['current_quater_name'] = kpicurrent_user_timperiod[0].kpi_time_period.name
        context['current_year_name'] = kpicurrent_user_timperiod[0].kpi_user_year.kpi_year.name
        return renderfile(request, 'kpi/levels', 'kpi_approve_detail_view', context)
        # else:
        #     return renderfile(request,pagename='404')

class EmployeeKPIApprove(LoginRequiredMixin,View):
    login_url = '/'   

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  
        comments = request.POST.get('comments', None)
        kpi_id = int(request.POST.get('kpi_id', 0))
        qtr_id = int(request.POST.get('qtr_id', 0))
        kpi_aproval_qryset = KPIApprovalStatus.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','approved_rejected_user','added_by','kpilevel_id')

        apprv_status =  kpi_aproval_qryset.filter(kpiuser_timeperiod__kpi_time_period=qtr_id,approved_rejected_user=kpi_id,added_by=request.user,action_type=KPI)
        apprv_status_last = apprv_status.last()
        
        if apprv_status.exists() and apprv_status.first().is_approve:
            if apprv_status.first().remarks != comments:
                apprv_status.update(remarks=comments.strip(),updated_at=datetime.now())
                messages.success(request, 'Comments updated')
            else:
                messages.success(request, 'KPI already approved')
        else:
            apprv_status.update(is_approve=True,remarks=comments.strip(),is_active=True,approval_status=APPROVED,updated_at=datetime.now())
            next_level = kpi_aproval_qryset.filter(approved_rejected_user = apprv_status_last.approved_rejected_user ,kpilevel_id=apprv_status_last.kpilevel_id.id+1,kpiuser_timeperiod__kpi_time_period=qtr_id,action_type=KPI)
            if next_level:
                next_level.update(approval_status=PENDING)
            
            # update hisensehr_kpiusertimeperiod status
            last_apprb_status = kpi_aproval_qryset.filter(kpiuser_timeperiod__kpi_time_period=qtr_id,approved_rejected_user=kpi_id,action_type=KPI).order_by('kpilevel_id')
            approval_count = last_apprb_status.count()
            kpi_timeperiod = KpiUserTimePeriod.objects.get(kpi_time_period=qtr_id, kpi_user_year__user_id=kpi_id)

            if approval_count == 0:
                # No approvals yet
                kpi_timeperiod.kpi_approval_status = 'pending' 
            elif approval_count == 1:
                # Only one approval level, set to final approval
                kpi_timeperiod.kpi_approval_status  = 'approved'
            else:
                # Multiple approval levels
                last_approval = last_apprb_status.last().kpilevel_id.id
                if last_approval != apprv_status_last.kpilevel_id.id:
                    # Only first level approved
                    kpi_timeperiod.kpi_approval_status = 'inprogress'
                else:
                    # Final level approved
                    kpi_timeperiod.kpi_approval_status = 'approved'

            if kpi_timeperiod.kpi_approval_status == 'approved':
                link = domainlink+'/my-kpi/'
                info = {}
                info['type'] = 'kpi_emp'
                info['action'] = 'Create'
                info['action_id'] = apprv_status_last.kpiuser_timeperiod.id
                info['user_id'] = kpi_id
                info['url'] = link
                info['kpi_time_period_id'] = qtr_id
                info['end_user'] = request.user.pk
                msg = f"Successfully completed your {apprv_status_last.kpiuser_timeperiod.kpi_time_period.name} kpi"
                notificationUpdate(user_from=request.user,
                                                    user_to=apprv_status_last.approved_rejected_user,
                                                    message=msg, info=info)
                #email notification                    
                mail_subject = 'KPI Approval Updates'
                user_name = apprv_status_last.approved_rejected_user.first_name+" "+apprv_status_last.approved_rejected_user.last_name
                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                send_email_to_users.delay(mail_subject, 4, content_replace, apprv_status_last.approved_rejected_user.email)

            messages.success(request, 'KPI approved successfully')
        kpi_timeperiod.save()
        # mail sent and notification table save
        next_assigned_user = next_level.first()
        if next_assigned_user:
            link = domainlink+'/kpi-approve-detail-view/'+encrypt_me(kpi_id)+'/'+encrypt_me(qtr_id)
            info = {}
            info['type'] = 'kpi_emp_approved'
            info['action'] = 'Create'
            info['action_id'] = apprv_status_last.kpiuser_timeperiod.id
            info['user_id'] = kpi_id
            info['url'] = link
            info['kpi_time_period_id'] = apprv_status.last().kpiuser_timeperiod.kpi_time_period.id
            info['end_user'] = request.user.pk
            msg = f"{next_assigned_user.approved_rejected_user.first_name} {next_assigned_user.approved_rejected_user.last_name}'s {apprv_status_last.kpiuser_timeperiod.kpi_time_period.name} kpi has been approved by {request.user.first_name} {request.user.last_name}, Now it's your turn"
            notificationUpdate(user_from=request.user,
                                                user_to=next_assigned_user.added_by,
                                                message=msg, info=info)
            #email notification                    
            mail_subject = 'KPI Employee Approved'
            user_name = next_assigned_user.added_by.first_name+" "+next_assigned_user.added_by.last_name
            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI employee approved","YEAR": datetime_timezone.now().year}
            send_email_to_users.delay(mail_subject, 4, content_replace, next_assigned_user.added_by.email)
            

        # link = domainlink+'/kpi-approve-detail-view/'+encrypt_me(kpi_id)+'/'+encrypt_me(qtr_id)
               
        # assigned_users = kpi_aproval_qryset.filter(kpilevel_id__gt=apprv_status.last().kpilevel_id,kpiuser_timeperiod__kpi_time_period=qtr_id,approved_rejected_user=kpi_id,action_type=KPI)
        # info = {}
        # info['type'] = 'kpi_emp_approved'
        # info['action'] = 'Create'
        # info['action_id'] = apprv_status_last.kpiuser_timeperiod.id
        # info['user_id'] = kpi_id
        # info['url'] = link
        # info['kpi_time_period_id'] = apprv_status.last().kpiuser_timeperiod.kpi_time_period.id
        # info['end_user'] = request.user.pk
        # for assigned_user in assigned_users:
        #     msg = f"{assigned_user.approved_rejected_user.first_name} {assigned_user.approved_rejected_user.last_name}'s {apprv_status_last.kpiuser_timeperiod.kpi_time_period.name} kpi has been approved by {request.user.first_name} {request.user.last_name}"
        #     notificationUpdate(user_from=request.user,
        #                                         user_to=assigned_user.added_by,
        #                                         message=msg, info=info)
        #     #email notification                    
        #     mail_subject = 'KPI employee approved'
        #     user_name = assigned_user.added_by.first_name+" "+assigned_user.added_by.last_name
        #     content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI employee approved"}
        #     send_email_to_users.delay(mail_subject, 4, content_replace, assigned_user.added_by.email)
        
        # save to remarks table
        KPIRemarks.objects.create(kpi_approvalstatus=apprv_status.first(),kpi_added_by=request.user,approval_status=APPROVED,remarks=comments.strip(),approve_reject_user_id=kpi_id)
        return HttpResponseRedirect(reverse_lazy('appdashboard:kpi_approve_detail_view', kwargs={'kpi': encrypt_me(kpi_id),'qtr':encrypt_me(qtr_id)}))

class EmployeeKPIReject(LoginRequiredMixin,View):
    login_url = '/'   

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  
        comments = request.POST.get('comments', None)
        kpi_id = int(request.POST.get('kpi_id', 0))
        qtr_id = int(request.POST.get('qtr_id', 0))
        # rejectedto_lvl = int(request.POST.get('assign_level', 0))

        kpi_aproval_qryset = KPIApprovalStatus.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','approved_rejected_user','added_by').filter(action_type=KPI)

        apprv_status =  kpi_aproval_qryset.filter(kpiuser_timeperiod__kpi_time_period=qtr_id,approved_rejected_user=kpi_id,added_by=request.user)

        if apprv_status.exists() and apprv_status.first().is_active == False and apprv_status.first().is_approve == True:
            if apprv_status.first().remarks != comments:
                apprv_status.update(remarks=comments.strip(),updated_at=datetime.now())
                messages.success(request, 'Comments updated')
            else:
                messages.success(request, 'KPI already rejected')
        else:
            rejectedto = kpi_aproval_qryset.get(kpiuser_timeperiod__kpi_time_period=qtr_id,approved_rejected_user=kpi_id,kpilevel_id_id=2)
            apprv_status.update(is_approve=False,remarks=comments.strip(),is_active=True,approval_status='rejected', rejected_to=rejectedto.added_by,updated_at=datetime.now())
            rest_apprv_status = kpi_aproval_qryset.filter(kpiuser_timeperiod__kpi_time_period=qtr_id,approved_rejected_user=kpi_id).exclude(kpilevel_id_id=apprv_status.last().kpilevel_id_id).update(is_approve=False,approval_status='pending')

            # update hisensehr_kpiusertimeperiod status
            kpi_user_time_prd = KpiUserTimePeriod.objects.get(kpi_time_period_id = qtr_id, kpi_user_year__user_id =kpi_id )
            kpi_user_time_prd.kpi_approval_status = 'rejected'
            kpi_user_time_prd.save()

      
            messages.success(request, 'KPI rejected successfully')
        
        # mail sent and notification table save
        link = domainlink+'/kpi-approve-detail-view/'+encrypt_me(kpi_id)+'/'+encrypt_me(qtr_id)
               
        assigned_users = kpi_aproval_qryset.filter(kpilevel_id__lt=apprv_status.last().kpilevel_id,kpiuser_timeperiod__kpi_time_period=qtr_id,approved_rejected_user=kpi_id)
        # msg = f"{apprv_status.last().kpiuser_timeperiod.kpi_time_period.name} kpi has been rejected by {request.user.first_name} {request.user.last_name}"
        msg = f"{apprv_status.last().approved_rejected_user.first_name} {apprv_status.last().approved_rejected_user.last_name}'s {apprv_status.last().kpiuser_timeperiod.kpi_time_period.name} kpi has been rejected by {request.user.first_name} {request.user.last_name}"
        info = {}
        info['type'] = 'kpi_emp_rejected'
        info['action'] = 'Create'
        info['action_id'] = apprv_status.last().kpiuser_timeperiod.id
        info['user_id'] = kpi_id
        info['url'] = link
        info['kpi_time_period_id'] = apprv_status.last().kpiuser_timeperiod.kpi_time_period.id
        info['end_user'] = request.user.pk
        for assigned_user in assigned_users:
            notificationUpdate(user_from=request.user,
                                                user_to=assigned_user.added_by,
                                                message=msg, info=info)
            #email notification                    
            mail_subject = 'KPI Employee Rejected'
            user_name = assigned_user.added_by.first_name+" "+assigned_user.added_by.last_name
            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI employee rejected","YEAR": datetime_timezone.now().year}
            send_email_to_users.delay(mail_subject, 4, content_replace, assigned_user.added_by.email)
        # save to remarks table
        KPIRemarks.objects.create(kpi_approvalstatus=apprv_status.first(),kpi_added_by=request.user,approval_status=REJECTED,remarks=comments.strip(),approve_reject_user_id=kpi_id)
        return HttpResponseRedirect(reverse_lazy('appdashboard:kpi_approve_detail_view', kwargs={'kpi': encrypt_me(kpi_id),'qtr':encrypt_me(qtr_id)}))

class EmployeeComments(LoginRequiredMixin,View):
    login_url = '/'
    
    @transaction.atomic   
    def post(self, request, *args, **kwargs):
        assignid = int(request.POST.get('assignid', None))
        employeeid = int(request.POST.get('employeeid', None))
        data  = KPIApprovalStatus.objects.filter(added_by_id=assignid,approved_rejected_user=employeeid).values('remarks','updated_at').first()

        if not data:
            data = {'remarks': 'error'}
        return JsonResponse(data)


# goal approvals
class GoalApprovalLevels(LoginRequiredMixin,View):
    login_url = '/'
    
    @transaction.atomic   
    def get(self, request, *args, **kwargs):
        context = {}
        page = request.GET.get('page', 1)
        context['kpi_levels'] = KPILevels.objects.filter(is_active=True,action_key=KPI).order_by('order')
        user_data = User.objects.select_related('profile', 'profile__department', 'profile__designation').\
            prefetch_related(Prefetch("kpilevel_user", queryset=KPILevelPermissions.objects.select_related('user_id', 'kpilevel_id', 'assigned_to').\
            filter(is_active=True,action_type=GOAL).order_by('id'), to_attr='kpi_perms')).  filter(is_active=True, profile__employement_type = 2).exclude(is_superuser=True).order_by(Lower('first_name'))
        paginator = Paginator(user_data, PAGINATION_PERPAGE)
        try:
            user_data = paginator.page(page)
        except PageNotAnInteger:
            user_data = paginator.page(1)
        except EmptyPage:
            user_data = paginator.page(paginator.num_pages)
        if type(page) != int:
            page = int(page)

        context['user_data'] = user_data
        context['current_page'] = page
        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
        return renderfile(request, 'kpi', 'levels', 'goal_level', context)

class EmployeeGoalLevelFilter(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpilevels'

    def get(self, request, *args, **kwargs):
        try:
            page = request.GET.get('page', 1)
            data = {}
            context = {}
            conditions = {}
            conditions['is_active'] = True
            name = request.GET.get('name', None)
            department = request.GET.get('department', None)
            designation = request.GET.get('designation', None)
            if department:
                conditions['profile__department_id'] = department
            if designation:
                conditions['profile__designation_id'] = designation

            user_data = User.objects.select_related('profile', 'profile__department', 'profile__designation').\
                prefetch_related(Prefetch("kpilevel_user", queryset=KPILevelPermissions.objects.select_related('user_id', 'kpilevel_id', 'assigned_to').\
                filter(is_active=True,action_type=GOAL).order_by('id'), to_attr='kpi_perms')).  filter(is_active=True, profile__employement_type = 2).exclude(is_superuser=True).order_by('first_name').\
                filter(**conditions).exclude(is_superuser=True).annotate(usersname=Concat('first_name', V(' '), 'last_name')).order_by(Lower('first_name'))

            if name:
                user_data = user_data.filter(usersname__icontains=name)

            paginator = Paginator(user_data, PAGINATION_PERPAGE)
            try:
                user_data = paginator.page(page)
            except PageNotAnInteger:
                user_data = paginator.page(1)
            except EmptyPage:
                user_data = paginator.page(paginator.num_pages)
            if type(page) != int:
                page = int(page)
            context['user_data'] = user_data
            context['current_page'] = page
            context['kpi_levels'] = KPILevels.objects.filter(is_active=True,action_key=KPI).order_by('order')
            template = render_to_string('hisensehr/kpi/levels/goal_level_ajax.html', context=context, request=request)
            data['pagination'] = render_to_string("hisensehr/kpi/levels/goal_level_pagination.html", context=context,
                                                  request=request)
            data['template'] = template
            data['status'] = True
        except Exception as e:
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class EmployeeGoalLevelForm(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.add_kpilevels'

    def get(self, request, *args, **kwargs):
        data = {}
        are_identical = False
        user_id = request.GET.get('user_id', None)
        if user_id:
            user = User.objects.select_related('profile').\
                prefetch_related(
                    Prefetch("kpilevel_user", 
                            queryset=KPILevelPermissions.objects.filter(is_active=True,action_type=GOAL).order_by('kpilevel_id'), 
                            to_attr='kpi_perms'),
                    Prefetch("kpilevel_user", 
                        queryset=KPILevelPermissions.objects.filter(is_active=True).order_by('kpilevel_id'), 
                        to_attr='kpi_permiss')
                ).get(id=user_id)

            # Filter the KPI permissions for goals and KPIs separately within the user object
            user_goal_perms = [perm for perm in user.kpi_permiss if perm.action_type == GOAL]
            user_kpi_perms = [perm for perm in user.kpi_permiss if perm.action_type == KPI]
            
            # Check if both goal and kpi workflows are the same
            user_goal_perms_data = [(perm.assigned_to, perm.kpilevel_id) for perm in user_goal_perms]
            user_kpi_perms_data = [(perm.assigned_to, perm.kpilevel_id) for perm in user_kpi_perms]
            if user_goal_perms and user_kpi_perms:
                are_identical = user_kpi_perms_data == user_goal_perms_data
            else:
                are_identical = False
        employees = User.objects.filter(is_active=True, profile__employement_type = 2).exclude(Q(id=user_id) | Q(is_superuser=True)).order_by(Lower('first_name'))
        kpi_levels = KPILevels.objects.filter(is_active=True,action_key=KPI).order_by('id')
        kpi_assigne = KpiActionPermission.objects.filter(user_id=user_id, kpi_action__key_action__in=['target_settings','actual_settings']).last()
        if kpi_assigne:
            kpi_assigne = kpi_assigne.assigned_to
        context = {'user': user, 'employees': employees, 'kpi_levels': kpi_levels, 'kpi_assigne':kpi_assigne, 'are_identical':are_identical}
        data['status'] = True
        data['template'] = render_to_string('hisensehr/kpi/levels/goal_level_form.html', context, request=request)
        return JsonResponse(data)

    def post(self, request, *args, **kwargs):
        data = {}
        levels = []
        levelIds = []
        db = None
        user_id = request.POST.get('user_id', None)
        kpi_goal_check = request.POST.get('kpi_goal_check')
        kpi_user = User.objects.only('id', 'first_name', 'last_name', 'is_active').get(id=int(user_id))

        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  

        # checking kpi-permission is set or not
        checklist = ['kpi_settings','kpi_goal','goal_settings','guideline_settings','target_settings','actual_settings']
        kpi_permission = KpiActionPermission.objects.select_related('user','kpi_action').filter(user_id=user_id, kpi_action__key_action__in=checklist)  
        if not kpi_permission:
            message = f"Please grant KPI permissions to {kpi_user.first_name+ ' ' +kpi_user.last_name} before configuring the Goal levels."
            data['message'] = message
            data['status'] = False
            return JsonResponse(data)

        # for levels
        kpi_level = KPILevels.objects.filter(action_key='kpi').order_by('order')
        for kpi_lvl in kpi_level:
            stage_lvl = request.POST.get(kpi_lvl.name, 0)
            if not stage_lvl:
                stage_lvl = str(kpi_lvl.id)+'$$0'
            sel_level_id, sel_usr = stage_lvl.split('$$')
            levels.append(int(sel_usr))
            levelIds.append(int(sel_level_id))
      
        non_zero_levels = [item for item in levels if item != 0]
        duplicates = len(non_zero_levels) != len(set(non_zero_levels))
        level_result_dict = dict(zip(levelIds, levels))

        if duplicates:
            data['duplicates'] = True
            return JsonResponse(data)
        
        is_active_users = User.objects.filter(id__in=non_zero_levels,is_active=True)
        if len(non_zero_levels) != is_active_users.count():
            permission_flag = 0
            not_active_users = User.objects.filter(id__in=non_zero_levels,is_active=False) 
            inactive_users_name =  [usr.first_name+ ' ' +usr.last_name for usr in not_active_users]
            inactive_users_id =  [usr.id for usr in not_active_users]
            if level_result_dict.get(2) in inactive_users_id:
                permission_flag = 1

            data['status'] = False
            if len(inactive_users_name) == 1:
                message = f"{inactive_users_name[0]} is inactive. Levels can't be updated."
            else:
                message = f"{', '.join(inactive_users_name)} are inactive. Levels can't be updated."
            if permission_flag == 1:
                message+=', change goal permission.'
            
            data['message'] = message
            return JsonResponse(data)      
        else:
            try:
                with transaction.atomic():
                    # CHECK THE USER HAS ANY PENDING IN-PROGRESS LEAVE REQUEST
                    kpi_approval_status = KPIApprovalStatus.objects.select_related('added_by','approved_rejected_user').filter(approved_rejected_user=user_id,action_type=GOAL)
                    dynamic_menu_id = GOAL_APPROVE_MENU
                    dynamic_menu_id_kpi = KPI_APPROVE_MENU
                    from_user = request.user.first_name + " " + request.user.last_name
                    get_user = get_object_or_404(User, id=user_id)
                    to_user = get_user.first_name + " " + get_user.last_name
                    log_msg = f"goal approval levels of user {to_user} has been updated."
                    log_msg_2 = f"{from_user} updated goal approval levels for user {to_user}"
                    current_levels = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=True,action_type=GOAL).order_by('kpilevel_id')
                    asssigned_users = [i.assigned_to_id for i in current_levels]
                    asssigned_users.insert(0, 0)
                    updated_users = [int(i) for i in levels if asssigned_users.count(int(i)) == 0 and i != 0]
                    # removing last user directly by selecting please select a new employee getting removed users
                    non_matching_values = []
                    # Check for matching values
                    for user in asssigned_users:
                        if user not in levels:
                            non_matching_values.append(user)

                    if current_levels:
                        kpi_useryear = KpiUserYear.objects.select_related('user').filter(user_id=user_id,status=2)
                        if kpi_useryear.exists():
                            data['status'] = False
                            data['message'] = 'Workflow cannot be modified after initiating the approval'
                            return JsonResponse(data)
                        elif updated_users:
                            current_usr_dlt = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=False,action_type=GOAL).delete()
                            level_ar = []
                            for level in current_levels:
                                level_assigned = level.assigned_to
                                if str(level_assigned.id) not in levels:
                                    level.is_active = False
                                    level.save()
                                    level_ar.append(level.kpilevel_id)
                            reverse_dict = {v: k for k, v in level_result_dict.items()}
                            edited_levls = []                              

                            for usr in updated_users:
                                lvlId = reverse_dict.get(usr)
                                edited_levls.append(lvlId)
                                userid = User.objects.get(id=usr)                                
                                # add user data updated to KPIApprovalStatus
                                apprval_chng = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpilevel_id').filter(Q(approved_rejected_user=user_id,kpilevel_id=lvlId,is_approve=False,approval_status=PENDING,action_type=GOAL)|Q(approved_rejected_user=user_id,kpilevel_id=lvlId,is_approve=False,approval_status=REJECTED,action_type=GOAL),kpi_user_year__kpi_year__current_year=True)
                                if apprval_chng:
                                    db = KPILevelPermissions.objects.create(user_id_id = user_id,kpilevel_id_id = lvlId,assigned_to = userid,action_type=GOAL)
                                    apprvl_status_chng = apprval_chng.update(added_by=userid)       
                                else:
                                    db = KPILevelPermissions.objects.create(user_id_id = user_id,kpilevel_id_id = lvlId,assigned_to = userid,action_type=GOAL)
                                    if kpi_approval_status:
                                        user_time_periods = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpilevel_id').filter(Q(approved_rejected_user=user_id,kpi_user_year__kpi_year__current_year=True)).values('kpi_user_year').distinct()
                                        try:
                                            kpi_year = KpiUserYear.objects.get(user_id=user_id, kpi_year__current_year=True, kpi_year__is_active=True)
                                            old_data_exists = KPIApprovalStatus.objects.select_related('approved_rejected_user', 'kpilevel_id').filter(
                                                Q(approved_rejected_user=user_id, kpilevel_id=lvlId, is_active=True, action_type=GOAL),
                                                kpi_user_year__kpi_year__current_year=True
                                            ).exists()
                                            
                                            if not old_data_exists and kpi_year.status != 3:
                                                KPIApprovalStatus.objects.create(
                                                    added_by=userid,
                                                    approved_rejected_user_id=user_id,
                                                    kpilevel_id_id=lvlId,
                                                    is_approve=False,
                                                    is_active=False,
                                                    approval_status=PENDING,
                                                    kpi_user_year_id=kpi_year.id,
                                                    action_type=GOAL
                                                )
                                        except KpiUserYear.DoesNotExist:
                                            pass
                                
                                if dynamic_menu_id: 
                                    # User_menu_access.objects.update_or_create(user_id=userid.id,  dynamic_menu_id = REQUESTS_KPI_MAIN_MENU, defaults={'dynamic_menu_id': REQUESTS_KPI_MAIN_MENU})
                                    # User_menu_access.objects.update_or_create(user_id=userid.id, dynamic_menu_id = dynamic_menu_id, defaults={'dynamic_menu_id': dynamic_menu_id})
                                    user_menu_access_queryset =  User_menu_access.objects.select_related('user','dynamic_menu')
                                    
                                    user_menu_access = user_menu_access_queryset.filter(user_id=userid.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = PERFORMANCE_CONFIG_MENU
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=userid.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU)

                                    user_menu_access = user_menu_access_queryset.filter(user_id=userid.id, dynamic_menu_id=dynamic_menu_id).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = dynamic_menu_id
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=userid.id, dynamic_menu_id=dynamic_menu_id)

                                # sending notification and mail to the selected users
                                lvl_usr = User.objects.get(id=int(usr))
                                link = domainlink+'/goal-approve-view/'
                                info = {}
                                info['type'] = 'kpi_emp'
                                info['action'] = 'Create'
                                info['action_id'] = db.id
                                info['user_id'] = user_id
                                info['url'] = link
                                # info['kpi_time_period_id'] = qtr_id
                                info['end_user'] = request.user.pk
                                msg = f"You have been added to {kpi_user.first_name} {kpi_user.last_name}'s goal approval team"
                                notificationUpdate(user_from=request.user,user_to=userid,message=msg, info=info)

                                #email notification                    
                                mail_subject = 'Goal Approval Updates'
                                user_name = userid.first_name+" "+userid.last_name
                                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"Goal approval updates","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, userid.email)

                                # notification and mail for removed user   
                                removed_usr = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,kpilevel_id_id = lvlId,is_active=False,action_type=GOAL).last()
                                if removed_usr:
                                    msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s goal approval team"
                                    notificationUpdate(user_from=request.user,user_to=removed_usr.assigned_to,message=msg, info=info)

                                    #email notification                    
                                    mail_subject = 'Goal Approval Updates'
                                    user_name = userid.first_name+" "+userid.last_name
                                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"Goal approval updates","YEAR": datetime_timezone.now().year}
                                    send_email_to_users.delay(mail_subject, 4, content_replace, removed_usr.assigned_to.email)
                                
                            
                            # for updating kpi permissions if the checkbox checked  
                            if kpi_goal_check == 'true':
                                kpi_timeperiod = KpiUserTimePeriod.objects.select_related('kpi_user_year','kpi_user_year__user').filter(kpi_user_year__user_id=user_id,kpi_approval_status='inprogress')
                                if kpi_timeperiod.exists():
                                    data['status'] = False
                                    data['message'] = 'Workflow cannot be modified after initiating the approval'
                                    return JsonResponse(data)
                                current_levels_kpi = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=True,action_type=KPI).order_by('id')
                                if current_levels_kpi:
                                    # deleting kpi workflow for updating with new one
                                    KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=True,action_type=KPI).order_by('id').delete()

                                    # indimating the kpi workflow users that they are removed
                                    for userdata in current_levels_kpi:
                                        if userdata.assigned_to.id not in level_result_dict.values():
                                            msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                            notificationUpdate(user_from=request.user,user_to=userdata.assigned_to,message=msg, info=info)

                                            #email notification                    
                                            mail_subject = 'Kpi Approval Updates'
                                            user_name = userid.first_name+" "+userid.last_name
                                            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                            send_email_to_users.delay(mail_subject, 4, content_replace, userdata.assigned_to.email)
                                    
                                    if dynamic_menu_id_kpi: 
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=userid.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).delete()
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=userid.id, dynamic_menu_id=dynamic_menu_id_kpi).delete()

                                users = []
                                for level, user in level_result_dict.items():
                                    if int(user) != 0 :
                                        db = KPILevelPermissions()
                                        db.user_id_id = user_id
                                        db.kpilevel_id_id = level
                                        db.assigned_to_id = int(user)
                                        db.action_type = KPI
                                        db.save()
                                        flush_cache('cached_menu_user')

                                        if dynamic_menu_id_kpi:  
                                            user_menu_access_queryset =  User_menu_access.objects.select_related('user','dynamic_menu')
                                            
                                            user_menu_access =user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).first()
                                            if user_menu_access is not None:
                                                user_menu_access.dynamic_menu_id = PERFORMANCE_CONFIG_MENU
                                                user_menu_access.save()
                                            else:
                                                User_menu_access.objects.create(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU)

                                            user_menu_access = user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=dynamic_menu_id_kpi).first()
                                            if user_menu_access is not None:
                                                user_menu_access.dynamic_menu_id = dynamic_menu_id_kpi
                                                user_menu_access.save()
                                            else:
                                                User_menu_access.objects.create(user_id=user, dynamic_menu_id=dynamic_menu_id_kpi)

                                            flush_cache('cached_menu_user')

                                        users.append(db.id)

                                        # sending notification and mail to the selected users
                                        lvl_usr = User.objects.get(id=int(user))
                                        link = domainlink+'/kpi-approve-view/'
                                        info = {}
                                        info['type'] = 'kpi_emp'
                                        info['action'] = 'Create'
                                        info['action_id'] = db.id
                                        info['user_id'] = user_id
                                        info['url'] = link
                                        # info['kpi_time_period_id'] = qtr_id
                                        info['end_user'] = request.user.pk
                                        msg = f"You have been added to {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                        notificationUpdate(user_from=request.user,user_to=lvl_usr,message=msg, info=info)

                                        #email notification                    
                                        mail_subject = 'KPI Approval Updates'
                                        user_name = lvl_usr.first_name+" "+lvl_usr.last_name
                                        content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                        send_email_to_users.delay(mail_subject, 4, content_replace, lvl_usr.email)

                                        
                               

                            KPILevelPermissions.objects.select_related('user_id','kpilevel_id').filter(user_id_id = user_id,action_type=GOAL).exclude(kpilevel_id_id__in = edited_levls).update(is_active = True)
                            flush_cache('cached_menu_user') 
                            
                            log_data = {}
                            log_data['module_name'] = 'employee_goal_approval_levels_updated'
                            log_data['action_type'] = UPDATE
                            log_data['log_message'] = log_msg
                            log_data['status'] = SUCCESS
                            log_data['model_object'] = db
                            log_data['db_data'] = {'id': updated_users, 'user': from_user, 'action': log_msg_2}
                            log_data['app_visibility'] = False
                            log_data['web_visibility'] = True
                            log_data['error_msg'] = ''
                            log_data['fwd_link'] = '/goal_approval_levels/'
                            LogUserActivity(request, log_data)

                            data['status'] = True
                            if kpi_goal_check == 'true':
                                data['message'] = 'Goal and KPI approval levels updated successfully'
                            else:
                                data['message'] = 'Goal approval levels updated successfully'

                        else:
                            if kpi_goal_check == 'true':
                                kpi_timeperiod = KpiUserTimePeriod.objects.select_related('kpi_user_year','kpi_user_year__user').filter(kpi_user_year__user_id=user_id,kpi_approval_status='inprogress')
                                if kpi_timeperiod.exists():
                                    data['status'] = False
                                    data['message'] = 'Workflow cannot be modified after initiating the approval'
                                    return JsonResponse(data)
                                current_levels_kpi = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=True,action_type=KPI).order_by('id')
                                if current_levels_kpi:
                                    # deleting kpi workflow for updating with new one
                                    KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=True,action_type=KPI).order_by('id').delete()
                                    link = domainlink+'/goal-approve-view/'
                                    info = {}
                                    info['type'] = 'kpi_emp'
                                    info['action'] = 'Create'
                                    info['action_id'] = 0
                                    info['user_id'] = user_id
                                    info['url'] = link
                                    # info['kpi_time_period_id'] = qtr_id
                                    info['end_user'] = request.user.pk                            
                                    # indimating the kpi workflow users that they are removed
                                    for userdata in current_levels_kpi:
                                        if userdata.assigned_to.id not in level_result_dict.values():
                                            msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                            notificationUpdate(user_from=request.user,user_to=userdata.assigned_to,message=msg, info=info)
                                            #email notification                    
                                            mail_subject = 'Kpi Approval Updates'
                                            user_name = userdata.assigned_to.first_name+" "+userdata.assigned_to.last_name
                                            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                            send_email_to_users.delay(mail_subject, 4, content_replace, userdata.assigned_to.email)
                                
                                        if dynamic_menu_id_kpi:
                                            User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=userdata.assigned_to.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).delete()
                                            User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=userdata.assigned_to.id, dynamic_menu_id=dynamic_menu_id_kpi).delete()

                                users = []
                                for level, user in level_result_dict.items():
                                    if int(user) != 0 :
                                        db = KPILevelPermissions()
                                        db.user_id_id = user_id
                                        db.kpilevel_id_id = level
                                        db.assigned_to_id = int(user)
                                        db.action_type = KPI
                                        db.save()
                                        flush_cache('cached_menu_user')

                                        if dynamic_menu_id_kpi:  
                                            user_menu_access_queryset =  User_menu_access.objects.select_related('user','dynamic_menu')
                                            
                                            user_menu_access =user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).first()
                                            if user_menu_access is not None:
                                                user_menu_access.dynamic_menu_id = PERFORMANCE_CONFIG_MENU
                                                user_menu_access.save()
                                            else:
                                                User_menu_access.objects.create(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU)

                                            user_menu_access = user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=dynamic_menu_id_kpi).first()
                                            if user_menu_access is not None:
                                                user_menu_access.dynamic_menu_id = dynamic_menu_id_kpi
                                                user_menu_access.save()
                                            else:
                                                User_menu_access.objects.create(user_id=user, dynamic_menu_id=dynamic_menu_id_kpi)

                                            flush_cache('cached_menu_user')

                                        users.append(db.id)

                                        # sending notification and mail to the selected users
                                        lvl_usr = User.objects.get(id=int(user))
                                        link = domainlink+'/kpi-approve-view/'
                                        info = {}
                                        info['type'] = 'kpi_emp'
                                        info['action'] = 'Create'
                                        info['action_id'] = db.id
                                        info['user_id'] = user_id
                                        info['url'] = link
                                        # info['kpi_time_period_id'] = qtr_id
                                        info['end_user'] = request.user.pk
                                        msg = f"You have been added to {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                        notificationUpdate(user_from=request.user,user_to=lvl_usr,message=msg, info=info)

                                        #email notification                    
                                        mail_subject = 'KPI Approval Updates'
                                        user_name = lvl_usr.first_name+" "+lvl_usr.last_name
                                        content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                        send_email_to_users.delay(mail_subject, 4, content_replace, lvl_usr.email)
                            
                        # removing last user directly by selecting please select a new employee           
                        if non_matching_values:
                            for re_user in non_matching_values:
                                rem_user = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,assigned_to_id=re_user,is_active=True,action_type=GOAL).last()
                                link = domainlink+'/goal-approve-view/'
                                info = {}
                                info['type'] = 'kpi_emp'
                                info['action'] = 'Create'
                                info['action_id'] = 0
                                info['user_id'] = user_id
                                info['url'] = link
                                # info['kpi_time_period_id'] = qtr_id
                                info['end_user'] = request.user.pk  
                                if rem_user:
                                    # deleting kpi workflow for updating with new one
                                    KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,assigned_to_id=re_user,is_active=True,action_type=GOAL).order_by('id').delete()
                                    
                                    # remove the user from the approval status table if it is not approved
                                    approval_status = KPIApprovalStatus.objects.select_related('approved_rejected_user').filter(approved_rejected_user_id=user_id, added_by_id=re_user, action_type=GOAL).last()
                                    if approval_status and approval_status.approval_status == 'pending':
                                        approval_status.delete()
                                        
                                    # indimating the kpi workflow users that they are removed
                                    msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s goal approval team"
                                    notificationUpdate(user_from=request.user,user_to=rem_user.assigned_to,message=msg, info=info)
                                    #email notification                    
                                    mail_subject = 'GOAL Approval Updates'
                                    user_name = rem_user.assigned_to.first_name+" "+rem_user.assigned_to.last_name
                                    content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"GOAL approval updates","YEAR": datetime_timezone.now().year}
                                    send_email_to_users.delay(mail_subject, 4, content_replace, rem_user.assigned_to.email)
                                    if dynamic_menu_id_kpi:
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=rem_user.assigned_to.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).delete()
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=rem_user.assigned_to.id, dynamic_menu_id=dynamic_menu_id_kpi).delete()
                        data['status'] = True
                        if kpi_goal_check == True:
                            data['message'] = 'Goal and KPI approval levels updated successfully'
                        else:
                            data['message'] = 'Goal approval levels updated successfully'
                        return JsonResponse(data)
                    
                    else:
                        if kpi_goal_check == 'true':
                            current_levels_kpi = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=True,action_type=KPI).order_by('id')
                            if current_levels_kpi:
                                # deleting kpi workflow for updating with new one
                                KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,is_active=True,action_type=KPI).order_by('id').delete()
                                link = domainlink+'/goal-approve-view/'
                                info = {}
                                info['type'] = 'kpi_emp'
                                info['action'] = 'Create'
                                info['action_id'] = 0
                                info['user_id'] = user_id
                                info['url'] = link
                                # info['kpi_time_period_id'] = qtr_id
                                info['end_user'] = request.user.pk                            
                                # indimating the kpi workflow users that they are removed
                                for userdata in current_levels_kpi:
                                    if userdata.assigned_to.id not in level_result_dict.values():
                                        msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                                        notificationUpdate(user_from=request.user,user_to=userdata.assigned_to,message=msg, info=info)
                                        #email notification                    
                                        mail_subject = 'Kpi Approval Updates'
                                        user_name = userdata.assigned_to.first_name+" "+userdata.assigned_to.last_name
                                        content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                                        send_email_to_users.delay(mail_subject, 4, content_replace, userdata.assigned_to.email)
                            
                                    if dynamic_menu_id_kpi: 
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=userdata.assigned_to.id, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).delete()
                                        User_menu_access.objects.select_related('user','dynamic_menu').filter(user_id=userdata.assigned_to.id, dynamic_menu_id=dynamic_menu_id_kpi).delete()

                        
                        users = []
                        for level, user in level_result_dict.items():
                            if int(user) != 0 :
                                db = KPILevelPermissions()
                                db.user_id_id = user_id
                                db.kpilevel_id_id = level
                                db.assigned_to_id = int(user)
                                db.action_type = GOAL
                                db.save()
                                # level_set = set(int(level) for level in levels if int(level) != 0)
                                # User_menu_access.objects.exclude(user_id__in=level_set).filter(dynamic_menu_id=dynamic_menu_id).delete()
                                flush_cache('cached_menu_user')

                                if dynamic_menu_id:
                                    user_menu_access_queryset =  User_menu_access.objects.select_related('user','dynamic_menu')

                                    user_menu_access = user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = PERFORMANCE_CONFIG_MENU
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU)

                                    # User_menu_access.objects.update_or_create(user_id=user,  dynamic_menu_id = REQUESTS_KPI_MAIN_MENU, defaults={'dynamic_menu_id': REQUESTS_KPI_MAIN_MENU})
                                    user_menu_access = user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=dynamic_menu_id).first()
                                    if user_menu_access is not None:
                                        user_menu_access.dynamic_menu_id = dynamic_menu_id
                                        user_menu_access.save()
                                    else:
                                        User_menu_access.objects.create(user_id=user, dynamic_menu_id=dynamic_menu_id)

                                    # User_menu_access.objects.update_or_create(user_id=user, dynamic_menu_id = dynamic_menu_id, defaults={'dynamic_menu_id': dynamic_menu_id})
                                    
                                    flush_cache('cached_menu_user')

                                # for kpi workflow creating
                                if kpi_goal_check == 'true':
                                    db = KPILevelPermissions()
                                    db.user_id_id = user_id
                                    db.kpilevel_id_id = level
                                    db.assigned_to_id = int(user)
                                    db.action_type = KPI
                                    db.save()

                                    flush_cache('cached_menu_user')
                                    if dynamic_menu_id_kpi:
                                        user_menu_access_queryset =  User_menu_access.objects.select_related('user','dynamic_menu')

                                        user_menu_access = user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU).first()
                                        if user_menu_access is not None:
                                            user_menu_access.dynamic_menu_id = PERFORMANCE_CONFIG_MENU
                                            user_menu_access.save()
                                        else:
                                            User_menu_access.objects.create(user_id=user, dynamic_menu_id=PERFORMANCE_CONFIG_MENU)

                                        user_menu_access = user_menu_access_queryset.filter(user_id=user, dynamic_menu_id=dynamic_menu_id_kpi).first()
                                        if user_menu_access is not None:
                                            user_menu_access.dynamic_menu_id = dynamic_menu_id_kpi
                                            user_menu_access.save()
                                        else:
                                            User_menu_access.objects.create(user_id=user, dynamic_menu_id=dynamic_menu_id_kpi)

                                        flush_cache('cached_menu_user')


                                users.append(db.id)

                                # sending notification and mail to the selected users
                                lvl_usr = User.objects.get(id=int(user))
                                link = domainlink+'/goal-approve-view/'
                                info = {}
                                info['type'] = 'kpi_emp'
                                info['action'] = 'Create'
                                info['action_id'] = db.id
                                info['user_id'] = user_id
                                info['url'] = link
                                # info['kpi_time_period_id'] = qtr_id
                                info['end_user'] = request.user.pk
                                msg = f"You have been added to {kpi_user.first_name} {kpi_user.last_name}'s goal approval team"
                                notificationUpdate(user_from=request.user,user_to=lvl_usr,message=msg, info=info)

                                #email notification                    
                                mail_subject = 'Goal Approval Updates'
                                user_name = lvl_usr.first_name+" "+lvl_usr.last_name
                                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"Goal approval updates","YEAR": datetime_timezone.now().year}
                                send_email_to_users.delay(mail_subject, 4, content_replace, lvl_usr.email)

                        if db == None:
                            data['status'] = False
                            data['message'] = 'Please select at least one level for an employee before saving.'
                            return JsonResponse(data)
                        
                        log_data = {}
                        log_data['module_name'] = 'employee_goal_approval_levels'
                        log_data['action_type'] = UPDATE
                        log_data['log_message'] = log_msg
                        log_data['status'] = SUCCESS
                        log_data['model_object'] = db
                        log_data['db_data'] = {'id': users, 'user': from_user, 'action': log_msg_2}
                        log_data['app_visibility'] = False
                        log_data['web_visibility'] = True
                        log_data['error_msg'] = ''
                        log_data['fwd_link'] = '/goal_approval_levels/'
                        LogUserActivity(request, log_data)
                        data['status'] = True
                        if kpi_goal_check == 'true':
                            data['message'] = 'Goal and KPI approval levels saved successfully'
                        else:
                            data['message'] = 'Goal approval levels saved successfully'
                        return JsonResponse(data)
            except Exception as dberror:
                import traceback;traceback.print_exc()
                log_data = {}
                log_data['module_name'] = 'employee_goal_approval_levels'
                log_data['action_type'] = CREATE
                log_data['log_message'] = 'employee goal approval level updation failed'
                log_data['status'] = FAILED
                log_data['model_object'] = None
                log_data['db_data'] = {}
                log_data['app_visibility'] = False
                log_data['web_visibility'] = False
                log_data['error_msg'] = traceback.format_exc()
                log_data['fwd_link'] = '/goal_approval_levels/'
                LogUserActivity(request, log_data)

                data['status'] = False
                data['message'] = "Something went wrong"
                return JsonResponse(data)

class EmployeeGoalLevelDeleteView(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.delete_kpiyear'

    def get(self, request, *args, **kwargs):
        data = {}
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain 
        try:
            with transaction.atomic():
                user_id = kwargs['id']
                dynamic_menu_id = GOAL_APPROVE_MENU
                dynamic_menu_id = KPI_APPROVE_MENU
                goal_data = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,action_type=GOAL)
                kpi_data = KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,action_type=KPI)
                kpi_useryear = KpiUserYear.objects.select_related('user','kpi_year').filter(user_id=user_id,kpi_year__current_year=True)
                kpi_timeperiod = KpiUserTimePeriod.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_user_year__kpi_year').filter(kpi_user_year__user_id=user_id,kpi_user_year__kpi_year__current_year=True)
                if goal_data:
                    if kpi_useryear.exists():
                        user_year_status = kpi_useryear.last().status
                        if user_year_status == INPROGRESS_STATUS:
                            data['status'] = False
                            data['message'] = "Workflow cannot be modified after initiating the approval"
                            return JsonResponse(data)
                        
                    KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,action_type=GOAL).delete()
                    
                    # remove the user from the approval status table if it is not approved in goal
                    if kpi_useryear.exists() and kpi_useryear.last().status not in [INPROGRESS_STATUS,APPROVED_STATUS]:
                        approval_status = KPIApprovalStatus.objects.select_related('approved_rejected_user').filter(approved_rejected_user_id=user_id, action_type=GOAL,kpi_user_year__kpi_year__current_year=True)
                        if approval_status:
                            approval_status.delete()
                    
                    usr_menu = User_menu_access.objects.select_related('user_id','dynamic_menu_id').exclude(user_id=user_id).filter(dynamic_menu_id=dynamic_menu_id)
                    if usr_menu.count() == 0:
                        usr_menu.delete()
                    from_user = request.user.first_name + " " + request.user.last_name
                    get_user = get_object_or_404(User, id=user_id)
                    to_user = get_user.first_name + " " + get_user.last_name
                    log_msg = f"goal permission levels of user {to_user} has been deleted."
                    log_msg_2 = f"{from_user} deleted goal permission levels of user {to_user}"
                    data['status'] = True

                    goal_user = User.objects.get(id=int(user_id))
                    for usr in goal_data:
                        # sending notification and mail to the selected users
                        link = domainlink+'/goal-approve-view/'
                        info = {}
                        info['type'] = 'goal_emp'
                        info['action'] = 'Create'
                        info['action_id'] = user_id
                        info['user_id'] = user_id
                        info['url'] = link
                        info['end_user'] = request.user.pk
                        # notification and mail for removed user   
                        msg = f"You have been removed from {goal_user.first_name} {goal_user.last_name}'s goal approval team"
                        notificationUpdate(user_from=request.user,user_to=usr.assigned_to,message=msg, info=info)

                        #email notification                    
                        mail_subject = 'Goal Approval Updates'
                        user_name = goal_user.first_name+" "+goal_user.last_name
                        content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"Goal approval updates","YEAR": datetime_timezone.now().year}
                        send_email_to_users.delay(mail_subject, 4, content_replace, usr.assigned_to.email)
                    
                    log_data = {}
                    log_data['module_name']  = 'goal_approval_levels'
                    log_data['action_type']  = DELETE
                    log_data['log_message']  = log_msg
                    log_data['status']  = SUCCESS
                    log_data['model_object']  =''
                    log_data['db_data']  = {'ids':'', 'user':from_user, 'action':log_msg_2}
                    log_data['app_visibility']  = False
                    log_data['web_visibility']  = True
                    log_data['error_msg']  = ''
                    log_data['fwd_link']  = '/goal_approval_levels/'
                    LogUserActivity(request, log_data)
                       
                if kpi_data:
                    if kpi_timeperiod.exists():
                        user_timeperiod_status = kpi_timeperiod.last().kpi_approval_status
                        if user_timeperiod_status == 'inprogress':
                            data['status'] = False
                            data['message'] = "Workflow cannot be modified after initiating the approval"
                            return JsonResponse(data)
                        
                    KPILevelPermissions.objects.select_related('user_id').filter(user_id=user_id,action_type=KPI).delete()

                    # remove the user from the approval status table if it is not approved in kpi
                    if kpi_timeperiod.exists() and kpi_timeperiod.last().kpi_approval_status not in ['inprogress','approved']:
                        approval_status = KPIApprovalStatus.objects.select_related('approved_rejected_user').filter(approved_rejected_user_id=user_id, action_type=KPI,kpiuser_timeperiod__kpi_user_year__kpi_year__current_year=True)
                        if approval_status:
                            approval_status.delete()

                    usr_menu = User_menu_access.objects.select_related('user_id','dynamic_menu').exclude(user_id=user_id).filter(dynamic_menu_id=dynamic_menu_id)
                    if usr_menu.count() == 0:
                        usr_menu.delete()
                    from_user = request.user.first_name + " " + request.user.last_name
                    get_user = get_object_or_404(User, id=user_id)
                    to_user = get_user.first_name + " " + get_user.last_name
                    log_msg = f"kpi permission levels of user {to_user} has been deleted."
                    log_msg_2 = f"{from_user} deleted kpi permission levels of user {to_user}"
                    data['status'] = True

                    kpi_user = User.objects.get(id=int(user_id))
                    for usr in kpi_data:
                        # sending notification and mail to the selected users
                        link = domainlink+'/kpi-approve-view/'
                        info = {}
                        info['type'] = 'kpi_emp'
                        info['action'] = 'Create'
                        info['action_id'] = user_id
                        info['user_id'] = user_id
                        info['url'] = link
                        info['end_user'] = request.user.pk
                        # notification and mail for removed user   
                        msg = f"You have been removed from {kpi_user.first_name} {kpi_user.last_name}'s kpi approval team"
                        notificationUpdate(user_from=request.user,user_to=usr.assigned_to,message=msg, info=info)

                        #email notification                    
                        mail_subject = 'KPI Approval Updates'
                        user_name = kpi_user.first_name+" "+kpi_user.last_name
                        content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI approval updates","YEAR": datetime_timezone.now().year}
                        send_email_to_users.delay(mail_subject, 4, content_replace, usr.assigned_to.email)
                        

                    log_data = {}
                    log_data['module_name']  = 'kpi_approval_levels'
                    log_data['action_type']  = DELETE
                    log_data['log_message']  = log_msg
                    log_data['status']  = SUCCESS
                    log_data['model_object']  =''
                    log_data['db_data']  = {'ids':'', 'user':from_user, 'action':log_msg_2}
                    log_data['app_visibility']  = False
                    log_data['web_visibility']  = True
                    log_data['error_msg']  = ''
                    log_data['fwd_link']  = '/kpi_approval_levels/'
                    LogUserActivity(request, log_data)
                    
                    
        except Exception as dberror:
            log_data = {}
            log_data['module_name'] = 'goal_approval_levels'
            log_data['action_type'] = DELETE
            log_data['log_message'] = 'employee goal permission level deletion failed'
            log_data['status'] = FAILED
            log_data['model_object'] = None
            log_data['db_data'] = {}
            log_data['app_visibility'] = False
            log_data['web_visibility'] = False
            log_data['error_msg'] = traceback.format_exc()
            log_data['fwd_link'] = '/goal_approval_levels/'
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class EmployeeGoalApproveView(LoginRequiredMixin, View):
    login_url = '/'

    def get(self, request, *args, **kwargs):
        context, data = {}, {}
        code = request.GET.get('code', None)
        
        # Get all approval statuses in one query with all necessary JOINs
        kpi_approval_filter = Q(
            added_by=request.user,
            kpi_user_year__kpi_year__current_year=True,
            kpi_user_year__kpi_year__is_active=True,
            kpi_user_year__status__in=[1,2],
            action_type=GOAL
        )
        
        if code:
            kpi_approval_filter &= Q(kpi_user_year__user__profile__employee_id=code)
        
        kpi_approval_statuses_list = list(KPIApprovalStatus.objects.select_related(
            'kpi_user_year__kpi_year', 'kpi_user_year__user', 'kpi_user_year__user__profile',
            'approved_rejected_user__profile__department', 'approved_rejected_user__profile__designation',
            'kpilevel_id', 'added_by__profile','kpilevel_id'
        ).filter(kpi_approval_filter).order_by(
            'kpi_user_year__user__first_name', 'kpi_user_year__user__last_name', 'kpilevel_id__id'
        ))
        
        # Sort by kpilevel_id as in original code
        kpi_approval_statuses_list = sorted(kpi_approval_statuses_list, key=lambda x: x.kpilevel_id.id)
        
        # Get distinct users
        seen_users = set()
        distinct_users_list = []
        for approval in kpi_approval_statuses_list:
            user_id = approval.approved_rejected_user.id
            if user_id not in seen_users:
                distinct_users_list.append(approval)
                seen_users.add(user_id)
        
        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
        context['filter_quater'] = KpiTimePeriod.objects.select_related('kpi_year').filter(is_active=True,kpi_year__current_year=True)
        context['kpi_year_filter'] = KpiYear.objects.filter(is_active=True).order_by('-start_date')
        context['kpi_year'] = KpiYear.objects.filter(is_active=True,current_year=True)
        context['kpi_approval_statuses_list'] = kpi_approval_statuses_list
        context['distinct_users_list'] = distinct_users_list
        context['current_user'] = request.user

        return renderfile(request, 'kpi/levels', 'goal_approve', context)
    
class EmployeeGoalApproveViewFilter(LoginRequiredMixin, View):
    login_url = '/'

    def get(self, request, *args, **kwargs):
        context, data = {}, {}
        search_key = request.GET.get('search_key', None)
        department = request.GET.get('department', None)
        designation = request.GET.get('designation', None)
        year = request.GET.get('year', None)
        status = request.GET.get('status', None)

        # Get all approval statuses in one optimized query
        kpi_approval_filter = Q(
            added_by=request.user,
            kpi_user_year__kpi_year__is_active=True,
            action_type=GOAL
        )
        
        kpi_approval_statuses_list = list(KPIApprovalStatus.objects.select_related(
            'kpi_user_year', 'kpi_user_year__kpi_year', 'kpi_user_year__user',
            'approved_rejected_user', 'approved_rejected_user__profile',
            'approved_rejected_user__profile__department', 'approved_rejected_user__profile__designation',
            'kpilevel_id', 'added_by', 'added_by__profile'
        ).filter(kpi_approval_filter).order_by('kpi_user_year__user__first_name', 'kpi_user_year__user__last_name', 'kpilevel_id__id'))
        
        # Sort by kpilevel_id as in original code
        kpi_approval_statuses_list = sorted(kpi_approval_statuses_list, key=lambda x: x.kpilevel_id.id)
        
        # Get distinct users
        seen_users = set()
        distinct_users_list = []
        for approval in kpi_approval_statuses_list:
            user_id = approval.approved_rejected_user.id
            if user_id not in seen_users:
                distinct_users_list.append(approval)
                seen_users.add(user_id)

        # Apply filters
        temp_distinct_users_list = distinct_users_list

        if search_key:
            search_words = search_key.lower().split()
            search_pattern = r'.*'.join(re.escape(word) for word in search_words)
            temp_distinct_users_list = [
                queryset for queryset in temp_distinct_users_list if (
                    re.search(search_pattern, queryset.approved_rejected_user.first_name.lower()) or
                    re.search(search_pattern, queryset.approved_rejected_user.last_name.lower()) or
                    re.search(search_pattern, queryset.approved_rejected_user.email.lower()) or
                    re.search(search_pattern, queryset.approved_rejected_user.profile.employee_id.lower()) or
                    re.search(search_pattern, (queryset.approved_rejected_user.first_name + ' ' + queryset.approved_rejected_user.last_name).lower())
                )
            ]

        if department:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list
                                        if int(department) == queryset.approved_rejected_user.profile.department.id]
        if designation:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list
                                        if int(designation) == queryset.approved_rejected_user.profile.designation.id]

        if year:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list
                                        if int(year) == queryset.kpi_user_year.kpi_year.id]
            kpi_year = KpiYear.objects.filter(is_active=True,id=int(year))
        else:
            kpi_year = KpiYear.objects.filter(is_active=True)

        if status:
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list if int(status) == queryset.kpi_user_year.status]
        else:
            status = [1,2]
            temp_distinct_users_list = [queryset for queryset in temp_distinct_users_list if queryset.kpi_user_year.status in status]

            
        distinct_users_list = temp_distinct_users_list

        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
        context['filter_quater'] = KpiTimePeriod.objects.select_related('kpi_year').filter(is_active=True,kpi_year__current_year=True)
        context['kpi_year'] = kpi_year
        context['kpi_approval_statuses_list'] = kpi_approval_statuses_list
        context['distinct_users_list'] = distinct_users_list
        context['current_user'] = request.user

        template = render_to_string('hisensehr/kpi/levels/goal_approve_ajax.html', context=context, request=request)
        data['template'] = template
        data['status'] = True
        return JsonResponse(data)
    
class EmployeeGoalApproveDetailView(LoginRequiredMixin,View):
    login_url = '/'   

    def get(self, request, *args, **kwargs):
        context, condition, data = {}, {}, {}
        try:
            year = decrypt_me(kwargs['yr'])
            usr_id = decrypt_me(kwargs['usr'])
        except:
            raise Http404
        button_flag = 1
        reject_button_flag = 1
        previous_approved_rejected_flag = 0

        kpi_user_year = KPIApprovalStatus.objects.select_related('approved_rejected_user').filter(
                    approved_rejected_user=usr_id,
                    kpi_user_year__kpi_year = year,
                    # kpi_user_year__kpi_year__current_year=True,
                    # kpi_user_year__kpi_year__is_active=True,
                    action_type=GOAL
                ).order_by('kpilevel_id')
        kpi_apprv_list = KPIApprovalStatus.objects.select_related(
            'kpiuser_timeperiod','approved_rejected_user','approved_rejected_user__profile__designation','added_by','kpilevel_id').\
                prefetch_related('kpi_approvalstatus').filter(kpi_user_year__kpi_year = year,approved_rejected_user_id=usr_id,action_type=GOAL)
        
        current_goal_aprv_usr = kpi_apprv_list.filter(added_by=request.user).last()
        goal_apprv_list_prev = kpi_apprv_list.filter(is_approve=False,kpilevel_id__lt=current_goal_aprv_usr.kpilevel_id)
        goal_apprv_list_nxt = kpi_apprv_list.filter(is_approve=True,kpilevel_id_id=(current_goal_aprv_usr.kpilevel_id_id+1))
        
        if current_goal_aprv_usr.kpilevel_id_id == 2:
            previous_approved_rejected_flag = 1
        else:
            previous_level = kpi_apprv_list.filter(kpi_user_year__kpi_year = year,approved_rejected_user_id=usr_id,kpilevel_id_id=(current_goal_aprv_usr.kpilevel_id_id-1)).last()
            if previous_level.is_active == True:
                previous_approved_rejected_flag = 1

        if goal_apprv_list_prev:
            button_flag=0

        if current_goal_aprv_usr.kpilevel_id_id == 2:
            reject_button_flag = 0 

        if button_flag == 1:
            if goal_apprv_list_nxt:
                button_flag = 0

        if current_goal_aprv_usr.is_active == True:
            context['approve_reject_status'] = current_goal_aprv_usr.approval_status
        # filter remarks based on quater
        kpi_remarks_list = KPIRemarks.objects.select_related('approve_reject_user','kpi_approvalstatus','kpi_approvalstatus__kpiuser_timeperiod').filter(approve_reject_user=usr_id, kpi_approvalstatus__kpi_user_year__kpi_year=year, kpi_approvalstatus__action_type = GOAL).order_by('id')

        user_ids = [str(request.user.id)]
        receiver_conditions = Q()
        sender_conditions = Q()
        for user_id in user_ids:
            receiver_conditions |= Q(receiver__contains=str(user_id))
        sender_conditions = Q(sender = request.user)
        # Add the condition to check if user_id_to_check is in the receiver field
        receiver_conditions |= Q(receiver__contains=user_ids)
        query_conditions = Q(
            is_active=True,
            kpi_user_id=usr_id,
            user_year__kpi_year=year,
            action_type=2
        ) & receiver_conditions 
    
        sender_query_conditions = Q(
            is_active=True,
            kpi_user_id=usr_id,
            user_year__kpi_year=year,
            action_type=2
        ) & sender_conditions

        approval_messages = ApprovalMessages.objects.filter(Q(query_conditions)|Q(sender_query_conditions)).order_by('-id')

        for message in approval_messages:
            receiver_id_list = ast.literal_eval(message.receiver)
            receiver_id = [int(id) for id in receiver_id_list]
            if receiver_id:
                user_data = User.objects.filter(id__in=receiver_id).values('first_name', 'last_name')
                user_names = ', '.join([f"{user['first_name']} {user['last_name']}" for user in user_data])
                message.receiver = user_names    
    
        context['current_goal_aprv_usr_status'] = current_goal_aprv_usr.is_approve
        context['kpi_apprv_list'] = kpi_user_year
        context['current_user'] = kpi_user_year.last()
        user_goals = KpiUserGoal.objects.select_related('kpi_user_year','kpi_user_year__kpi_year','kpi_user_year__user').filter(kpi_user_year__kpi_year=year,kpi_user_year__user=usr_id,is_active=True).values('id','kpi_goal__kpi_category__name','kpi_goal__measurement_type__name','kpi_goal__name','goal_value','max_finish_rate','weight').order_by('id')
        context['user_goals'] = user_goals
        context['total_weight'] = sum(item['weight'] for item in user_goals)
        # context['guideline'] = get_kpi_guideline(apprv_reject_id, current_kpi_year.id)
        context['button_flag'] = button_flag
        context['reject_button_flag'] = reject_button_flag
        context['yearid'] = year
        context['kpi_remarks_list'] = kpi_remarks_list
        context['approval_messages'] = approval_messages
        context['previous_level'] = previous_approved_rejected_flag
        # context['current_appv'] = kpi_apprv_list.filter(added_by_id = request.user).last()
        return renderfile(request, 'kpi/levels', 'goal_approve_detail_view', context)
    

class EmployeeGoalApprove(LoginRequiredMixin,View):
    login_url = '/'   

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  
        comments = request.POST.get('comments', None)
        kpi_id = int(request.POST.get('kpi_id', 0))
        yearid = int(request.POST.get('yearid', 0))

        kpi_aproval_qryset = KPIApprovalStatus.objects.select_related('kpi_user_year','kpi_user_year__kpi_year','approved_rejected_user','added_by','kpilevel_id')

        apprv_status =  kpi_aproval_qryset.filter(kpi_user_year__kpi_year=yearid,approved_rejected_user=kpi_id,added_by=request.user,action_type=GOAL)
        apprv_status_last = apprv_status.last()

        apprv_status.update(is_approve=True,remarks=comments,is_active=True,approval_status=APPROVED,updated_at=datetime.now())
        next_level = kpi_aproval_qryset.filter(approved_rejected_user=kpi_id,kpilevel_id=apprv_status_last.kpilevel_id.id+1,kpi_user_year__kpi_year=yearid,action_type=GOAL)
        if next_level:
            next_level.update(approval_status=PENDING)
        
        approve_user_status =  kpi_aproval_qryset.filter(kpi_user_year__kpi_year=yearid,approved_rejected_user=kpi_id,action_type=GOAL).order_by('kpilevel_id')
        # update hisensehr_kpiusertimeperiod status
        approval_count = approve_user_status.count()
        goal_user_year = KpiUserYear.objects.get(kpi_year_id=yearid, user_id=kpi_id)
        if approval_count == 0:
            # No approvals yet
            goal_user_year.status = PENDING_STATUS 
        elif approval_count == 1:
            # Only one approval level, set to final approval
            goal_user_year.status = APPROVED_STATUS
        else:
            # Multiple approval levels
            last_approval = approve_user_status.last().kpilevel_id.id
            if last_approval != apprv_status_last.kpilevel_id.id:
                # Only first level approved
                goal_user_year.status = INPROGRESS_STATUS
            else:
                # Final level approved
                goal_user_year.status = APPROVED_STATUS

        if goal_user_year.status == APPROVED_STATUS:
            # notification and mail to created user
            kpi_first_level = kpi_aproval_qryset.filter(kpi_user_year__kpi_year=yearid,approved_rejected_user=kpi_id,kpilevel_id_id=2,action_type=GOAL).last()
            link = domainlink+'/kpi-time-periods/'
            
            info = {}
            info['type'] = 'goal_emp_approved'
            info['action'] = 'Create'
            info['action_id'] = apprv_status_last.kpi_user_year.id
            info['user_id'] = kpi_id
            info['url'] = link
            # info['kpi_time_period_id'] = apprv_status.last().kpiuser_timeperiod.kpi_time_period.id
            info['end_user'] = request.user.pk

            msg = f"{apprv_status_last.approved_rejected_user.first_name} {apprv_status_last.approved_rejected_user.last_name}'s goal level approvals have been successfully completed. Please upload the KPI values"
            notificationUpdate(user_from=request.user,
                                                user_to=kpi_first_level.added_by,
                                                message=msg, info=info)
            #email notification                    
            mail_subject = 'Goal Approval Updates'
            user_name = apprv_status_last.added_by.first_name+" "+apprv_status_last.added_by.last_name
            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"Goal employee approved","YEAR": datetime_timezone.now().year}
            send_email_to_users.delay(mail_subject, 4, content_replace, apprv_status_last.added_by.email)


            messages.success(request, 'Goal approved successfully')
        goal_user_year.save()

        # mail sent and notification table save
        next_assigned_user = next_level.first()
        if next_assigned_user:
            link = domainlink+'/goal-approve-detail-view/'+encrypt_me(kpi_id)+'/'+encrypt_me(yearid) 
                
            info = {}
            info['type'] = 'goal_emp_approved'
            info['action'] = 'Create'
            info['action_id'] = apprv_status_last.kpi_user_year.id
            info['user_id'] = kpi_id
            info['url'] = link
            # info['kpi_time_period_id'] = apprv_status.last().kpiuser_timeperiod.kpi_time_period.id
            info['end_user'] = request.user.pk

            msg = f"{next_assigned_user.approved_rejected_user.first_name} {next_assigned_user.approved_rejected_user.last_name}'s Goal has been approved by {request.user.first_name} {request.user.last_name}, Now it's your turn"
            notificationUpdate(user_from=request.user,
                                                user_to=next_assigned_user.added_by,
                                                message=msg, info=info)
            #email notification                    
            mail_subject = 'Goal Employee Approved'
            user_name = next_assigned_user.added_by.first_name+" "+next_assigned_user.added_by.last_name
            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"Goal employee approved","YEAR": datetime_timezone.now().year}
            send_email_to_users.delay(mail_subject, 4, content_replace, next_assigned_user.added_by.email)

       

        # save to remarks table
        KPIRemarks.objects.create(kpi_approvalstatus=apprv_status.first(),kpi_added_by=request.user,approval_status=APPROVED,remarks=comments,approve_reject_user_id=kpi_id)
        # return HttpResponseRedirect(reverse_lazy('appdashboard:goal_approve_detail_view', kwargs={'usr': encrypt_me(kpi_id),'yr':encrypt_me(yearid)}))
        return HttpResponseRedirect(reverse_lazy('appdashboard:goal_approve_view'))

class EmployeeGoalReject(LoginRequiredMixin,View):
    login_url = '/'   

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  
        comments = request.POST.get('comments', None)
        kpi_id = int(request.POST.get('kpi_id', 0))
        yearid = int(request.POST.get('yearid', 0))
        # rejectedto_lvl = int(request.POST.get('assign_level', 0))

        kpi_aproval_qryset = KPIApprovalStatus.objects.select_related('kpilevel_id','kpi_user_year','kpi_user_year__kpi_year','approved_rejected_user','added_by')

        apprv_status =  kpi_aproval_qryset.filter(kpi_user_year__kpi_year=yearid,approved_rejected_user=kpi_id,added_by=request.user,action_type=GOAL)

        if apprv_status.exists() and apprv_status.first().is_active == False and apprv_status.first().is_approve == True:
            if apprv_status.first().remarks != comments:
                apprv_status.update(remarks=comments)
                messages.success(request, 'Comments updated')
            else:
                messages.success(request, 'KPI already rejected')
        else:
            rejectedto = kpi_aproval_qryset.get(kpi_user_year__kpi_year=yearid,approved_rejected_user=kpi_id,kpilevel_id_id=2,action_type=GOAL)
            apprv_status.update(is_approve=False,remarks=comments,is_active=True,approval_status='rejected', rejected_to=rejectedto.added_by)
            rest_apprv_status = kpi_aproval_qryset.filter(kpi_user_year__kpi_year=yearid,approved_rejected_user=kpi_id).exclude(kpilevel_id_id=apprv_status.last().kpilevel_id_id).update(is_approve=False,approval_status='pending')

            goal_user_year= KpiUserYear.objects.get(kpi_year_id = yearid, user_id =kpi_id )
            goal_user_year.status = 4
            goal_user_year.save()

            messages.success(request, 'KPI rejected successfully')
        
        # mail sent and notification table save
        link = domainlink+'/goal-approve-detail-view/'+encrypt_me(kpi_id)+'/'+encrypt_me(yearid)
               
        assigned_users = kpi_aproval_qryset.filter(kpilevel_id__lt=apprv_status.last().kpilevel_id,kpi_user_year__kpi_year=yearid,approved_rejected_user=kpi_id)
        msg = f"{rejectedto.approved_rejected_user.first_name} {rejectedto.approved_rejected_user.last_name}'s goal has been rejected by {request.user.first_name} {request.user.last_name}"
        info = {}
        info['type'] = 'kpi_emp_rejected'
        info['action'] = 'Create'
        info['action_id'] = apprv_status.last().kpi_user_year.id
        info['user_id'] = kpi_id
        info['url'] = link
        info['end_user'] = request.user.pk
        for assigned_user in assigned_users:
            notificationUpdate(user_from=request.user,
                                                user_to=assigned_user.added_by,
                                                message=msg, info=info)
            #email notification                    
            mail_subject = 'KPI Employee Rejected'
            user_name = assigned_user.added_by.first_name+" "+assigned_user.added_by.last_name
            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI employee rejected","YEAR": datetime_timezone.now().year}
            send_email_to_users.delay(mail_subject, 4, content_replace, assigned_user.added_by.email)
        # save to remarks table
        KPIRemarks.objects.create(kpi_approvalstatus=apprv_status.first(),kpi_added_by=request.user,approval_status=REJECTED,remarks=comments,approve_reject_user_id=kpi_id)
        return HttpResponseRedirect(reverse_lazy('appdashboard:goal_approve_detail_view', kwargs={'usr': encrypt_me(kpi_id),'yr':encrypt_me(yearid)}))

# message between kpi levels
class ApprovalMessage(LoginRequiredMixin,View):
    login_url = '/'   

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        data = {}
        user_checked_values = request.POST.getlist('userChecked[]')
        message = request.POST.get('message')
        kpi_user = request.POST.get('kpi_user')
        qtr = request.POST['qtr']
        yearid = request.POST['yearid']
        scheme = request.is_secure() and "https://" or "http://"
        current_site = get_current_site(request)
        domainlink=scheme+current_site.domain  
        info = {}
        try:
            if int(qtr) != 0 and int(yearid) == 0:
                user_timeperiod = KpiUserTimePeriod.objects.get(kpi_time_period_id=int(qtr),kpi_user_year__user=int(kpi_user))
                apprv_msg = ApprovalMessages.objects.create(message=message,sender=request.user,receiver=user_checked_values,kpi_user_id=int(kpi_user),user_timeperiod=user_timeperiod,action_type=1)
                link = domainlink+'/kpi-approve-detail-view/'+encrypt_me(kpi_user)+'/'+encrypt_me(int(qtr))
                info['type'] = 'kpi_approval_msg'

            elif int(qtr) == 0 and int(yearid) != 0:
                user_year = KpiUserYear.objects.get(kpi_year_id=int(yearid),user=int(kpi_user))
                apprv_msg = ApprovalMessages.objects.create(message=message,sender=request.user,receiver=user_checked_values,kpi_user_id=int(kpi_user),user_year=user_year,action_type=2)
                link = domainlink+'/goal-approve-detail-view/'+encrypt_me(kpi_user)+'/'+encrypt_me(int(yearid))
                info['type'] = 'goal_approval_msg'
            
            for assigned_user in user_checked_values:
                info['action'] = 'Create'
                info['action_id'] = apprv_msg.id
                info['user_id'] = int(assigned_user)
                info['url'] = link
                info['end_user'] = request.user.pk
                user = User.objects.get(id=int(assigned_user))
                msg = f"You've just received a new message from {request.user.first_name} {request.user.last_name} !"
                notificationUpdate(user_from=request.user,
                                                    user_to=user,
                                                    message=msg, info=info)
                #email notification                    
                mail_subject = 'New Message Received 📬'
                user_name = user.first_name+" "+user.last_name
                content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"New Message Received","YEAR": datetime_timezone.now().year}
                send_email_to_users.delay(mail_subject, 4, content_replace, user.email)
            data['status'] = True
            data['message'] = "Message sent successfully"
        except Exception as e:
            data['status'] = False
            data['message'] = f"Error: {str(e)}"
        return JsonResponse(data)

# KPI performance - role based
class KPIPerformance(PermissionRequiredMixin,View):
    permission_required = 'hisensehr.view_kpilevels'

    @transaction.atomic
    def get(self,request,*args, **kwargs):
        context = {}
        kpi_apprv_distinct_usr = []
        kpi_approval_statuses_list = []
        page = request.GET.get('page', 1)
        current_quarter = KpiTimePeriod.objects.select_related('kpi_year').filter(kpi_year__current_year=True,kpi_year__is_active=True).order_by('-start_date')
        if current_quarter:
            for qtr in current_quarter:
                kpi_approval = KPIApprovalStatus.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','approved_rejected_user','kpilevel_id','approved_rejected_user__profile',
                                                        'approved_rejected_user__profile__designation','approved_rejected_user__profile__department','added_by','added_by__profile').\
                    filter(action_type=KPI,kpiuser_timeperiod__kpi_time_period=qtr,kpiuser_timeperiod__kpi_approval_status__in=['approved'])
                kpi_apprv_distinct_usr.append(kpi_approval.distinct('approved_rejected_user'))
                kpi_approval_statuses_list.extend(kpi_approval)
                if kpi_approval.exists():
                    break
                else:
                    pass
            kpi_approval_statuses_list = sorted(kpi_approval_statuses_list, key=lambda x: x.kpilevel_id.id)
            distinct_users_list = [item for sublist in kpi_apprv_distinct_usr for item in sublist]
            distinct_users_list = sorted(
                distinct_users_list,
                key=lambda x: (
                    x.approved_rejected_user.first_name.lower() if x.approved_rejected_user and x.approved_rejected_user.first_name else '',
                    x.approved_rejected_user.last_name.lower() if x.approved_rejected_user and x.approved_rejected_user.last_name else ''
                )
            )
            
            paginator = Paginator(distinct_users_list, PAGINATION_PERPAGE)
            try:
                distinct_users_list = paginator.page(page)
            except PageNotAnInteger:
                distinct_users_list = paginator.page(1)
            except EmptyPage:
                distinct_users_list = paginator.page(paginator.num_pages)
            if type(page) != int:
                page = int(page)

            context['quater'] = qtr
            context['current_page'] = page
            context['current_user'] = request.user
            context['kpi_approval'] = kpi_approval
            context['distinct_users_list'] = distinct_users_list
            context['kpi_year'] = KpiYear.objects.filter(is_active=True).order_by('-start_date')
            context['filter_quater'] = KpiTimePeriod.objects.select_related('kpi_year').filter(is_active=True,kpi_year__current_year=True)
            context['departments'] = Department.objects.filter(is_active=True).order_by('name')
            context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
            context['kpi_approval_statuses_list'] = kpi_approval_statuses_list
            return renderfile(request, 'kpi', 'performance','kpi_performance', context)
        
# filter quater for year selection
class KPIPerformanceFetchQuater(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpilevels'

    def get(self, request, *args, **kwargs):
        context = {}
        data = {}
        condition = {'kpi_year__is_active':True}
        try:
            year = request.GET.get('year', None)
            if year:
                condition['kpi_year_id']=int(year)
            kpi_time_period = KpiTimePeriod.objects.select_related('kpi_year').filter(**condition)
            context['filter_quater'] = kpi_time_period
            context['quater'] =  kpi_time_period.first()
            template = render_to_string('hisensehr/kpi/performance/kpi_performance_filter_ajax.html', context=context, request=request)
            data['template'] = template
            data['status'] = True
        except Exception as e:
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

# kpi performance - filters
class KPIPerformanceFilter(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpilevels'

    def get(self, request, *args, **kwargs):
        try:
            page = request.GET.get('page', 1)
            data = {}
            context = {}
            context_filter = {}
            conditions = {}
            condition = {}
            kpi_apprv_distinct_usr = []
            kpi_approval_statuses_list = []
            distinct_users_list = []
            conditions['is_active'] = True
            name = request.GET.get('search_key', None)
            department = request.GET.get('department', None)
            designation = request.GET.get('designation', None)
            year = request.GET.get('year', None)
            quater = request.GET.get('quater', '')
            previous_year = request.GET.get('previous_year', None)
            status = request.GET.get('status', None)
            condition = {'kpi_year__is_active':True}

            if year:
                condition['kpi_year_id']=int(year)
            else:
                current_kpi_year = KpiYear.objects.filter(current_year=True).first()
                if current_kpi_year:
                    condition['kpi_year_id'] = current_kpi_year.id
            kpi_time_period = KpiTimePeriod.objects.select_related('kpi_year').filter(**condition)
            context_filter['filter_quater'] = kpi_time_period
            context_filter['qtr'] = ''
            context['quater'] = kpi_time_period
            context['current_year'] = year

            if year != previous_year:
                kpi_time_period = KpiTimePeriod.objects.select_related('kpi_year').filter(**condition, is_active=True)
                context_filter['filter_quater'] = kpi_time_period
                context_filter['qtr'] = kpi_time_period.first()
                context['quater'] = kpi_time_period.first()
                qtr = kpi_time_period.first()
                quarter = kpi_time_period.first()
                if len(kpi_time_period) > 1:
                    kpi_time_period = kpi_time_period[:1]
                    ids = [item.id for item in kpi_time_period]  # Get the IDs from the sliced list
                    kpi_time_period = KpiTimePeriod.objects.filter(id__in=ids)

            else:
                if quater != '':
                    kpi_time_period = KpiTimePeriod.objects.select_related('kpi_year').filter(id=int(quater), **condition)
                    context_filter['qtr'] = int(quater)
                    context['quater'] = kpi_time_period
                    qtr = quater 

            condition = {}
            if year:
                condition['kpiuser_timeperiod__kpi_time_period__kpi_year_id'] = int(year)
            else:
                current_kpi_year = KpiYear.objects.filter(current_year=True).first()
                if current_kpi_year:
                    condition['kpiuser_timeperiod__kpi_time_period__kpi_year_id'] = current_kpi_year.id
            if quater == '':
                kpi_approval = KPIApprovalStatus.objects.select_related('kpiuser_timeperiod', 'kpiuser_timeperiod__kpi_time_period', 'kpiuser_timeperiod__kpi_time_period__kpi_year', 'approved_rejected_user', 'kpilevel_id', 'approved_rejected_user__profile',
                                                                        'approved_rejected_user__profile__designation', 'approved_rejected_user__profile__department', 'added_by', 'added_by__profile').\
                    filter(action_type=KPI, **condition)
            else:                
                kpi_approval = KPIApprovalStatus.objects.select_related('kpiuser_timeperiod', 'kpiuser_timeperiod__kpi_time_period', 'kpiuser_timeperiod__kpi_time_period__kpi_year', 'approved_rejected_user', 'kpilevel_id', 'approved_rejected_user__profile',
                                                                        'approved_rejected_user__profile__designation', 'approved_rejected_user__profile__department', 'added_by', 'added_by__profile').\
                    filter(action_type=KPI, kpiuser_timeperiod__kpi_time_period=qtr, **condition)
                           

            # Ensure no duplicate users within the same quarter
            if kpi_approval:
                kpi_approval_statuses_list.extend(kpi_approval)

            distinct_users_list = []
            user_quarter_map = set()  # To track unique user-quarter combinations

            for approval in kpi_approval_statuses_list:
                user = approval.approved_rejected_user
                quarter = approval.kpiuser_timeperiod.kpi_time_period

                if (user.id, quarter.id) not in user_quarter_map:
                    user_quarter_map.add((user.id, quarter.id))
                    distinct_users_list.append(approval)

            # Apply search and filter conditions to the distinct_users_list
            if name:
                name_parts = name.lower().split()  # Split the name into parts
                
                def user_name_match(user, part):
                    approved_user = user.approved_rejected_user
                    # Check first name
                    if approved_user.first_name and part in approved_user.first_name.lower():
                        return True
                    # Check last name
                    if approved_user.last_name and part in approved_user.last_name.lower():
                        return True
                    # Check nick_name safely
                    profile = getattr(approved_user, 'profile', None)
                    if profile:
                        nick = getattr(profile, 'nick_name', None)
                        if nick and part in nick.lower():
                            return True
                    return False
                
                distinct_users_list = [
                    user for user in distinct_users_list if any(
                        user_name_match(user, part) for part in name_parts
                    )
                ]

            if department:
                distinct_users_list = [
                    user for user in distinct_users_list if user.approved_rejected_user.profile.department_id == int(department)
                ]

            if designation:
                distinct_users_list = [
                    user for user in distinct_users_list if user.approved_rejected_user.profile.designation_id == int(designation)
                ]

            if status:
                distinct_users_list = [
                    user for user in distinct_users_list if status == user.kpiuser_timeperiod.kpi_approval_status
                ]
            else:
                status = ['pending', 'inprogress']
                distinct_users_list = [
                    user for user in distinct_users_list if user.kpiuser_timeperiod.kpi_approval_status in status
                ]

            # show no data for empty case
            filter_quarter_dict = {quarter.id: 0 for quarter in context_filter['filter_quater']}
            for user in distinct_users_list:
                user_quarter = user.kpiuser_timeperiod.kpi_time_period
                if user_quarter.id in filter_quarter_dict:
                    filter_quarter_dict[user_quarter.id] += 1

            # Remove the KpiTimePeriod objects from the QuerySet where the count is 0
            filtered_quarter_ids_to_remove = [quarter_id for quarter_id, count in filter_quarter_dict.items() if count == 0]
            if kpi_time_period:
                kpi_time_period = kpi_time_period.exclude(id__in=filtered_quarter_ids_to_remove)

            distinct_users_list = sorted(
                distinct_users_list,
                key=lambda x: (
                    x.approved_rejected_user.first_name.lower() if x.approved_rejected_user and x.approved_rejected_user.first_name else '',
                    x.approved_rejected_user.last_name.lower() if x.approved_rejected_user and x.approved_rejected_user.last_name else ''
                )
            )

            # Always use scroll functionality instead of pagination for better user experience
            # This ensures all records are shown with scroll regardless of timeperiod selection
            context['show_all_quarters'] = True
            context['paginated_users'] = distinct_users_list

            if year != previous_year:
                template_filter = render_to_string('hisensehr/kpi/performance/kpi_performance_filter_ajax.html', context=context_filter, request=request)
                data['template_filter'] = template_filter
            else:
                data['template_filter'] = ''


            context['quater'] = kpi_time_period
            context['current_user'] = request.user
            context['kpi_approval'] = kpi_approval
            context['current_page'] = page
            context['distinct_users_list'] = context.get('paginated_users', distinct_users_list)
            context['departments'] = Department.objects.filter(is_active=True).order_by('name')
            context['designations'] = Designation.objects.filter(is_active=True).order_by('name')
            context['kpi_approval_statuses_list'] = kpi_approval_statuses_list

            template = render_to_string('hisensehr/kpi/performance/kpi_performance_ajax.html', context=context, request=request)
            # data['pagination'] = render_to_string("hisensehr/kpi/performance/kpi_performance_pagination.html", context=context, request=request)
            data['template'] = template
            data['status'] = True
        except Exception as e:
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

# Kpi performance - Role based - detail view
class KPIPerformanceDetailView(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpilevels'

    def get(self, request, *args, **kwargs):
            context, condition, data = {}, {}, {}
            try:
                qtr = decrypt_me(kwargs['qtr'])
                usr = decrypt_me(kwargs['usr'])
                isedit = decrypt_me(kwargs['isedit'])
            except:
                raise Http404
            current_kpi_year = KpiYear.objects.get(current_year=True,is_active=True)

            kpi_user_year = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period').filter(
                        approved_rejected_user=usr,
                        kpiuser_timeperiod__kpi_time_period_id=qtr,
                        action_type=KPI
                    ).order_by('kpilevel_id')
            
            # filter remarks based on quater
            kpi_remarks_list = KPIRemarks.objects.select_related('approve_reject_user','kpi_approvalstatus','kpi_approvalstatus__kpiuser_timeperiod').filter(approve_reject_user=usr, kpi_approvalstatus__kpiuser_timeperiod__kpi_time_period_id=qtr, kpi_approvalstatus__action_type = KPI).order_by('id')

            context['kpi_apprv_list'] = kpi_user_year
            context['current_user'] = kpi_user_year.last()
            # show calculation
            user_goals = KPIUserGoalValues.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','kpiuser_goal','kpiuser_goal__kpi_user_year','kpiuser_goal__kpi_user_year__user','kpiuser_goal__kpi_goal__measurement_type','kpiuser_goal__kpi_goal__kpi_category')\
            .filter(kpiuser_timeperiod__kpi_time_period=qtr,kpiuser_goal__kpi_user_year__user=usr)
            
            modified_log = None

            # Check if updated_score is not None and get the modified log
            if user_goals and user_goals[0].kpiuser_timeperiod.updated_score is not None:
                performance_modified_log = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=user_goals[0].kpiuser_timeperiod)
                if performance_modified_log:
                    # group the modified log based on the value type(cricism and appreciation)
                    criticism_list = [log for log in performance_modified_log if log.value_type == 1]
                    appreciation_list = [log for log in performance_modified_log if log.value_type == 2]
                    # sum up the values for both criticism and appreciation
                    criticism_value_sum = sum(log.value for log in criticism_list)
                    appreciation_value_sum = sum(log.value for log in appreciation_list)
                    # pass thosee values to the html
                    context['criticism_list'] = criticism_list
                    context['appreciation_list'] = appreciation_list
                    context['criticism_value_sum'] = criticism_value_sum
                    context['appreciation_value_sum'] = appreciation_value_sum

                    # previous_score = user_goals[0].kpiuser_timeperiod.score
                    # updated_criticism_score = 0
                    # updated_appreciation_score = 0
                    # if criticism_value_sum:  # Criticism
                    #     updated_criticism_score = previous_score - ((previous_score * criticism_value_sum)/100)
                    # if appreciation_value_sum:  # Appreciation
                    #     updated_appreciation_score = previous_score + ((previous_score * appreciation_value_sum)/100)

                    modified_log= performance_modified_log.last()
                

            # fetch data from performance table for performance accordian

            # modified_log = None
            context['user_goals'] = user_goals
            context['guideline'] = get_kpi_guideline(usr, current_kpi_year.id)
            context['kpi_remarks_list'] = kpi_remarks_list
            context['current_period_id'] = qtr
            context['modified_log'] = modified_log
            context['current_quater_name'] = kpi_user_year[0].kpiuser_timeperiod.kpi_time_period.name
            context['year_name'] = kpi_user_year[0].kpiuser_timeperiod.kpi_user_year.kpi_year.name
            context['isedit'] = isedit
            return renderfile(request, 'kpi/performance', 'performance_detail_view', context)

class DeletePerformanceModification(PermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpilevels'
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        log_id = int(request.POST.get('log_id'))
        timeperiod = int(request.POST.get('timeperiod'))
        previous_score = Decimal(request.POST.get('previous_score'))
        # Get the performance modification log table object
        try:
            log_data = PerformanceModificationLog.objects.get(id=log_id)
        except PerformanceModificationLog.DoesNotExist:
            return JsonResponse({'status': False, 'message': 'Performance modification log not found'})
        if log_data:
            kpi_user_timeperiod = KpiUserTimePeriod.objects.get(id=timeperiod)
            PerformanceModificationLog.objects.get(id=log_id).delete()
            performance_prev_value = PerformanceModificationLog.objects.filter(kpi_user_timeperiod=kpi_user_timeperiod)
            criticism_list = [log for log in performance_prev_value if log.value_type == 1]
            appreciation_list = [log for log in performance_prev_value if log.value_type == 2]
            
            # sum up the values for both criticism and appreciation
            criticism_value_sum = sum(log.value for log in criticism_list)
            appreciation_value_sum = sum(log.value for log in appreciation_list)
            if log_data.value_type == 1:
                criticism_value_sum = criticism_value_sum +  Decimal(log_data.value)
            else:
                appreciation_value_sum = appreciation_value_sum +  Decimal(log_data.value)
                
            if criticism_value_sum > appreciation_value_sum:
                criticism_flag = 1
                new_revision_value = criticism_value_sum - appreciation_value_sum
            else:
                criticism_flag = 0
                new_revision_value = appreciation_value_sum - criticism_value_sum
            previous_score = round(Decimal(previous_score),2)

            # Calculate the updated score based on the revision type
            if criticism_flag == 1:  # Criticism
                updated_score = previous_score - ((previous_score * new_revision_value)/100)
            elif criticism_flag == 0:  # Appreciation
                updated_score = previous_score + ((previous_score * new_revision_value)/100)
            updated_score = None
            # save the final updated score in kpitimeperiod and performance modiifcation log
            prev_score = previous_score
            criticism_value_sum = 0
            appreciation_value_sum = 0
            for perf_data in performance_prev_value:                
                # sum up the values for both criticism and appreciation
                if perf_data.value_type == 1:
                    criticism_value_sum = criticism_value_sum +  Decimal(perf_data.value)
                else:
                    appreciation_value_sum = appreciation_value_sum +  Decimal(perf_data.value)
                    
                if criticism_value_sum > appreciation_value_sum:
                    criticism_flag = 1
                    new_revision_value = criticism_value_sum - appreciation_value_sum
                else:
                    criticism_flag = 0
                    new_revision_value = appreciation_value_sum - criticism_value_sum
                previous_score = round(Decimal(previous_score),2)

                # Calculate the updated score based on the revision type 
                if criticism_flag == 1:  # Criticism
                    updated_score = previous_score - ((previous_score * new_revision_value)/100)
                elif criticism_flag == 0:  # Appreciation
                    updated_score = previous_score + ((previous_score * new_revision_value)/100)

                perf_data.updated_score = updated_score
                perf_data.previous_score = prev_score
                perf_data.save()
                prev_score = updated_score
            if updated_score == None:
                kpi_user_timeperiod.performance_comment = None
            kpi_user_timeperiod.updated_score = updated_score
            kpi_user_timeperiod.save()

            # previous calculation code
            # remaining_logData = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod = timeperiod)
            # previous_score = log_data.previous_score
            # if remaining_logData:
            #     for logData in remaining_logData:
            #         if logData.value_type == 1:
            #             updated_score = previous_score - ((previous_score * log_data.value)/100)
            #         else:   
            #             updated_score = previous_score + ((previous_score * log_data.value)/100)
            #         # save the updated score to the current iterated log data
            #         logData.previous_score = previous_score
            #         logData.updated_score = updated_score
            #         logData.save()
            #         # updating the perverse score with new updated score
            #         previous_score = updated_score
                
            #     # save the final updated score in kpitimeperiod
            #     kpi_user_timeperiod.updated_score = updated_score
            #     kpi_user_timeperiod.save()
            # else:
            #     kpi_user_timeperiod.updated_score = None
            #     kpi_user_timeperiod.performance_comment = None
            #     kpi_user_timeperiod.save()
            user_goals = KPIUserGoalValues.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','kpiuser_goal','kpiuser_goal__kpi_user_year','kpiuser_goal__kpi_user_year__user','kpiuser_goal__kpi_goal__measurement_type','kpiuser_goal__kpi_goal__kpi_category')\
                .filter(kpiuser_timeperiod=timeperiod)
            context = {
                'user_goals': user_goals,
                'criticism_list': criticism_list,
                'appreciation_list': appreciation_list,
                'criticism_value_sum': criticism_value_sum,
                'appreciation_value_sum': appreciation_value_sum,
                'modified_log': PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=kpi_user_timeperiod).last(),
            }

            template = render_to_string('hisensehr/kpi/performance/performance_detail_ajax.html', context=context, request=request)
            return JsonResponse({'status': True, 'message': 'Log removed successfully', 'template': template})

        else:
            return JsonResponse({'status': False, 'message':'Something went wrong'})



# view employee status in popup - Import/Export 
class EmployeeListStatus(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        context, data = {}, {}
        user_id = request.GET.get('user_id', None)
        timeperiod = request.GET.get('timeperiod', None)
        user_id_list = ast.literal_eval(user_id)
        
        action_permissions = KpiActionPermission.objects.values(
            'user__first_name', 'user__last_name', 'user_id', 'user__profile__profile_pic'
        ).annotate(
            kpi_status=Subquery(
                KpiUserYear.objects.filter(
                    user_id=OuterRef('user_id'),
                    kpi_year__current_year=True
                ).values('status')[:1]
            )
        ).filter(
            kpi_action__key_action__in=['goal_settings', 'target_settings', 'actual_settings'],
            # assigned_to_id=request.user.id, # to list all employees for hr commented
            user_id__in=user_id_list
        ).distinct('user_id')

        employee_list = [
            {
                'first_name': ap['user__first_name'],
                'last_name': ap['user__last_name'],
                'employee_id': ap['user_id'],
                'profile_pic': ap['user__profile__profile_pic'],
                'kpi_approval_status': {'status': ap['kpi_status']},
            }
            for ap in action_permissions
        ]

        context['employee_list'] = employee_list
        data['template'] = render_to_string('hisensehr/kpi/overview/employee_status_list.html', context, request=request)
        data['status'] = True
        return JsonResponse(data)
    
class SavePerformanceModification(LoginRequiredMixin, View):
    login_url = '/'

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        # Extract data from the POST request
        try:
            revision_type = int(request.POST.get('revision_type'))
            revision_value = float(request.POST.get('revision_value'))
            reason = request.POST.get('reason')
            uploaded_file = request.FILES.get('file')
            modification_date = request.POST.get('modification_date')
            reason = request.POST.get('reason')
            timeperiod_id = int(request.POST.get('timeperiod'))
            previous_score = float(request.POST.get('previous_score'))
        except (TypeError, ValueError) as e:
            return JsonResponse({'success': False, 'error': 'Invalid input data'})

        # Get the KpiUserTimePeriod object
        try:
            kpi_user_timeperiod = KpiUserTimePeriod.objects.get(id=timeperiod_id)
        except KpiUserTimePeriod.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Timeperiod not found'})
        performance_prev_value = PerformanceModificationLog.objects.filter(kpi_user_timeperiod=kpi_user_timeperiod)
        criticism_list = [log for log in performance_prev_value if log.value_type == 1]
        appreciation_list = [log for log in performance_prev_value if log.value_type == 2]
        
        # sum up the values for both criticism and appreciation
        criticism_value_sum = sum(log.value for log in criticism_list)
        appreciation_value_sum = sum(log.value for log in appreciation_list)
        if revision_type == 1:
            criticism_value_sum = criticism_value_sum +  Decimal(revision_value)
        else:
            appreciation_value_sum = appreciation_value_sum +  Decimal(revision_value)
            
        if criticism_value_sum > appreciation_value_sum:
            criticism_flag = 1
            new_revision_value = criticism_value_sum - appreciation_value_sum
        else:
            criticism_flag = 0
            new_revision_value = appreciation_value_sum - criticism_value_sum
        previous_score = round(Decimal(previous_score),2)

        # Calculate the updated score based on the revision type
        if criticism_flag == 1:  # Criticism
            updated_score = previous_score - ((previous_score * new_revision_value)/100)
        elif criticism_flag == 0:  # Appreciation
            updated_score = previous_score + ((previous_score * new_revision_value)/100)
        else:
            return JsonResponse({'success': False, 'error': 'Invalid revision type'})


        PerformanceModificationLog.objects.create(
            kpi_user_timeperiod=kpi_user_timeperiod,added_by=request.user,previous_score=previous_score,
            updated_score=updated_score,value_type=revision_type,value=revision_value,comments=reason,
            documents = uploaded_file, modification_date = modification_date,
        )

        message = 'Performance modification saved successfully'

        # Update the KpiUserTimePeriod object
        kpi_user_timeperiod.updated_score = updated_score
        kpi_user_timeperiod.performance_comment = reason
        kpi_user_timeperiod.save()

        user_goals = KPIUserGoalValues.objects.select_related('kpiuser_timeperiod','kpiuser_timeperiod__kpi_time_period','kpiuser_goal','kpiuser_goal__kpi_user_year','kpiuser_goal__kpi_user_year__user','kpiuser_goal__kpi_goal__measurement_type','kpiuser_goal__kpi_goal__kpi_category')\
                .filter(kpiuser_timeperiod=kpi_user_timeperiod)
        if is_ajax(request=request):
            performance_prev_value = PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=kpi_user_timeperiod)
            criticism_list = [log for log in performance_prev_value if log.value_type == 1]
            appreciation_list = [log for log in performance_prev_value if log.value_type == 2]
            criticism_value_sum = sum(log.value for log in criticism_list)
            appreciation_value_sum = sum(log.value for log in appreciation_list)
        context = {
            'user_goals': user_goals,
            'criticism_list': criticism_list,
            'appreciation_list': appreciation_list,
            'criticism_value_sum': criticism_value_sum,
            'appreciation_value_sum': appreciation_value_sum,
            'modified_log': PerformanceModificationLog.objects.select_related('kpi_user_timeperiod').filter(kpi_user_timeperiod=timeperiod_id).last(),
        }

        template = render_to_string('hisensehr/kpi/performance/performance_detail_ajax.html', context=context, request=request)
        return JsonResponse({'success': True, 'message': message, 'template': template})


class KpiAutocompleteView(LoginRequiredMixin, View):
    login_url = '/'

    def get(self, request, *args, **kwargs):
        user_id = request.GET.get('user_id')
        term = request.GET.get('term', '').strip()

        data = {'status': False, 'kpis': []}

        try:
            if user_id:
                # Get department from user
                user = User.objects.select_related('profile__department').get(id=user_id)
                department_id = user.profile.department.id if hasattr(user, 'profile') and user.profile.department else None
                
                if department_id:
                    # Search for KPIs that match the term (case-insensitive)
                    kpis = KpiCategory.objects.filter(
                        department_id=department_id,
                        is_active=True
                    ).prefetch_related(
                        Prefetch(
                            'kpi_goals',
                            queryset=KpiGoal.objects.filter(is_active=True).select_related('measurement_type'),
                            to_attr='active_goals'
                        )
                    )
                    
                    if term:
                        # Split the search term and search for each word
                        search_terms = term.split()
                        for search_term in search_terms:
                            kpis = kpis.filter(name__icontains=search_term)
                    
                    kpis = kpis.order_by('name')[:10]  # Limit to 10 results
            kpi_list = []
            for kpi in kpis:
                goals_data = []
                for goal in kpi.active_goals:
                    goal_data = {
                        'id': goal.id,
                        'name': goal.name,
                        'measurement_type': None
                    }
                    if goal.measurement_type:
                        goal_data['measurement_type'] = {
                            'id': goal.measurement_type.id,
                            'name': goal.measurement_type.name
                        }
                    goals_data.append(goal_data)

                kpi_list.append({
                    'id': kpi.id,
                    'name': kpi.name,
                    'goals': goals_data
                })
                data['status'] = True
                data['kpis'] = kpi_list

        except Exception as e:
            data['status'] = False
            data['error'] = str(e)

        return JsonResponse(data)


class GoalAutocompleteView(LoginRequiredMixin, View):
    login_url = '/'

    def get(self, request, *args, **kwargs):
        user_id = request.GET.get('user_id')
        term = request.GET.get('term', '').strip()

        data = {'status': False, 'goals': []}

        try:
            if user_id:
                # Get department from user
                user = User.objects.select_related('profile__department').get(id=user_id)
                department_id = user.profile.department.id if hasattr(user, 'profile') and user.profile.department else None
                
                if department_id:
                    # Search for Goals that match the term (case-insensitive)
                    goals = KpiGoal.objects.filter(
                        kpi_category__department_id=department_id,
                        is_active=True
                    ).select_related(
                        'kpi_category', 'measurement_type'
                    )
                    
                    if term:
                        goals = goals.filter(name__icontains=term)
                    
                    goals = goals.order_by('name')[:10]  # Limit to 10 results
                                
                goal_list = []
                for goal in goals:
                    goal_data = {
                        'id': goal.id,
                        'name': goal.name,
                        'kpi_id': goal.kpi_category.id,
                        'kpi_name': goal.kpi_category.name,
                        'measurement_type': None
                    }
                    if goal.measurement_type:
                        goal_data['measurement_type'] = {
                            'id': goal.measurement_type.id,
                            'name': goal.measurement_type.name
                        }
                    goal_list.append(goal_data)
                
                    data['status'] = True
                    data['goals'] = goal_list

        except Exception as e:
            data['status'] = False
            data['error'] = str(e)

        return JsonResponse(data)


class CheckDuplicateKpiGoalView(LoginRequiredMixin, View):
    login_url = '/'

    def post(self, request, *args, **kwargs):
        data = {'status': 'success'}

        try:
            goal_name = request.POST.get('goal_name', '').strip()
            category_name = request.POST.get('category_name', '').strip()
            user_id = request.POST.get('user_id')
            period_id = request.POST.get('period_id')
            current_user_goal_id = request.POST.get('current_user_goal_id')

            if not all([goal_name, category_name, user_id, period_id]):
                data['status'] = 'error'
                data['message'] = 'Missing required parameters'
                return JsonResponse(data)

            # Get the KPI Year for the period
            try:
                time_period = KpiTimePeriod.objects.get(id=period_id)
                kpi_year = time_period.kpi_year
            except KpiTimePeriod.DoesNotExist:
                data['status'] = 'error'
                data['message'] = 'Invalid period'
                return JsonResponse(data)

            # Get user year
            try:
                user_year = KpiUserYear.objects.get(
                    user_id=user_id,
                    kpi_year=kpi_year,
                    is_active=True
                )
            except KpiUserYear.DoesNotExist:
                data['status'] = 'error'
                data['message'] = 'User year not found'
                return JsonResponse(data)

            # Check for existing KPI-Goal combination
            existing_goals = KpiUserGoal.objects.filter(
                kpi_user_year=user_year,
                is_active=True
            ).select_related('kpi_goal', 'kpi_goal__kpi_category')

            # Exclude current record if editing
            if current_user_goal_id:
                existing_goals = existing_goals.exclude(id=current_user_goal_id)

            # Check for duplicate
            for existing_goal in existing_goals:
                if (existing_goal.kpi_goal.name.lower() == goal_name.lower() and
                    existing_goal.kpi_goal.kpi_category.name.lower() == category_name.lower()):
                    data['status'] = 'duplicate'
                    data['message'] = f'The KPI "{category_name}" with Goal "{goal_name}" is already assigned to this user for the selected period.'
                    break

        except Exception as e:
            data['status'] = 'error'
            data['error'] = str(e)

        return JsonResponse(data)


class EditKpiGoalView(LoginRequiredMixin, View):
    login_url = '/'

    def post(self, request, *args, **kwargs):
        data = {'status': False}

        try:
            with transaction.atomic():
                user_goal_id = request.POST.get('user_goal_id')
                user_id = request.POST.get('user_id')
                kpi_name = request.POST.get('category_name', '').strip()
                goal_name = request.POST.get('goal_name', '').strip()
                kpi_category_id = request.POST.get('kpi_category_id')
                measurement_type_id = int(request.POST.get('measurement_type'))
                goal_value = request.POST.get('goal_value', '').strip()
                max_finish_rate = float(request.POST.get('max_finish_rate', 0))
                weight = float(request.POST.get('weight', 0))

                # Check if the same goal and KPI already exist for this employee
                old_goal = KpiUserGoal.objects.filter(id=user_goal_id).select_related('kpi_goal__kpi_category').first()
                if old_goal:
                    if (old_goal.kpi_goal.name.lower() == goal_name.lower() and
                        old_goal.kpi_goal.kpi_category.name.lower() == kpi_name.lower() and 
                        old_goal.kpi_goal.measurement_type_id == measurement_type_id and 
                        old_goal.goal_value == goal_value and 
                        old_goal.max_finish_rate == max_finish_rate and 
                        old_goal.weight == weight):
                           status_val = True

                    else:
                       status_val = False

                if (not status_val and (
                    old_goal.kpi_goal.name.lower() != goal_name.lower() or
                    old_goal.kpi_goal.kpi_category.name.lower() != kpi_name.lower())):
                    existing_goal = KpiUserGoal.objects.filter(
                        kpi_user_year__user_id=user_id,
                        kpi_goal__name=goal_name,
                        kpi_goal__kpi_category__name=kpi_name,
                        kpi_user_year__kpi_year__current_year=True
                    ).exists()

                    if existing_goal:
                        return JsonResponse({
                            'status': False,
                            'message': 'The same goal and KPI already exist for this employee.'
                        })

                # Get the existing user goal
                try:
                    user_goal = KpiUserGoal.objects.select_related(
                        'kpi_goal__kpi_category', 'kpi_user_year'
                    ).get(id=user_goal_id)
                except KpiUserGoal.DoesNotExist:
                    data['message'] = 'User goal not found'
                    return JsonResponse(data)

                # Validate weight - total should not exceed 100%
                if weight:
                    try:
                        weight_value = float(weight)
                        # Get total weight excluding current record
                        total_weight = KpiUserGoal.objects.filter(
                            kpi_user_year=user_goal.kpi_user_year,
                            is_active=True
                        ).exclude(id=user_goal_id).aggregate(
                            total=Sum('weight')
                        )['total'] or 0

                        if float(total_weight) + weight_value > 100:
                            data['message'] = 'Total weight should not exceed 100%'
                            return JsonResponse(data)
                    except ValueError:
                        data['message'] = 'Invalid weight value'
                        return JsonResponse(data)

                # Get user for department validation
                user = User.objects.select_related('profile__department').get(id=user_id)
                current_kpi = user_goal.kpi_goal.kpi_category
                current_goal = user_goal.kpi_goal

                # Handle KPI/Goal logic
                if kpi_name and goal_name:
                    # Same KPI + same Goal → Use existing
                    if (current_kpi.name.lower() == kpi_name.lower() and
                        current_goal.name.lower() == goal_name.lower()):
                        if measurement_type_id and str(current_goal.measurement_type_id) != str(measurement_type_id):
                            current_goal.measurement_type_id = measurement_type_id
                            current_goal.save()

                    # Same KPI + different Goal → Create new KPI for new goal
                    elif current_kpi.name.lower() == kpi_name.lower():
                        # If kpi_category_id exists, use it to get the specific KPI category
                        if kpi_category_id:
                            try:
                                new_kpi = KpiCategory.objects.get(id=kpi_category_id, is_active=True)
                            except KpiCategory.DoesNotExist:
                                # If kpi_category_id doesn't exist, create new KPI
                                new_kpi = KpiCategory.objects.create(
                                    name=kpi_name,
                                    department_id=user.profile.department_id,
                                    is_active=True
                                )
                        else:
                            # If no kpi_category_id, create new KPI (since we want each goal to have its own KPI)
                            new_kpi = KpiCategory.objects.create(
                                name=kpi_name,
                                department_id=user.profile.department_id,
                                is_active=True
                            )

                        old_goal = KpiGoal.objects.filter(
                            name=goal_name,
                            kpi_category=new_kpi,
                            is_active=True,
                        ).first()

                        if not old_goal:
                            # Create new goal under the new KPI
                            new_goal = KpiGoal.objects.create(
                                name=goal_name,
                                kpi_category=new_kpi,
                                measurement_type_id=measurement_type_id,
                                is_active=True,
                                added_by=request.user
                            )
                            user_goal.kpi_goal = new_goal
                        else:
                            user_goal.kpi_goal = old_goal

                    # Different KPI → Create new KPI and goal
                    else:
                        # If kpi_category_id exists, use it to get the specific KPI category
                        if kpi_category_id:
                            try:
                                existing_kpi = KpiCategory.objects.get(id=kpi_category_id, is_active=True)
                            except KpiCategory.DoesNotExist:
                                # If kpi_category_id doesn't exist, create new KPI
                                existing_kpi = KpiCategory.objects.create(
                                    name=kpi_name,
                                    department_id=user.profile.department_id,
                                    is_active=True
                                )
                        else:
                            # If no kpi_category_id, create new KPI (since we want each goal to have its own KPI)
                            existing_kpi = KpiCategory.objects.create(
                                name=kpi_name,
                                department_id=user.profile.department_id,
                                is_active=True
                            )

                        existing_goal = KpiGoal.objects.filter(
                            kpi_category=existing_kpi,
                            name__iexact=goal_name,
                            is_active=True
                        ).first()

                        if not existing_goal:
                            existing_goal = KpiGoal.objects.create(
                                name=goal_name,
                                kpi_category=existing_kpi,
                                measurement_type_id=measurement_type_id,
                                is_active=True,
                                added_by=request.user
                            )
                        user_goal.kpi_goal = existing_goal

                # Update user goal values
                if goal_value:
                    user_goal.goal_value = goal_value
                if max_finish_rate:
                    user_goal.max_finish_rate = float(max_finish_rate)
                if weight:
                    user_goal.weight = float(weight)

                user_goal.is_active = status_val
                user_goal.save()
                if not status_val:
                    KpiUserGoal.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_user_year__kpi_year').filter(
                        kpi_user_year__user_id=user_id,
                        kpi_user_year__kpi_year__current_year=True
                    ).update(is_active=False)
                data['status'] = True
                data['message'] = 'Successfully updated'

        except Exception as e:
            import traceback;traceback.print_exc()
            data['message'] = f'Error: {str(e)}'

        return JsonResponse(data)


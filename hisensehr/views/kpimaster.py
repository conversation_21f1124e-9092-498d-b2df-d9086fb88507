import pdb
from django.urls import resolve
from django.db import transaction, IntegrityError
from django.template import loader
from django.http import HttpResponse, JsonResponse
from django.views.generic.base import View
from django.db.models.functions import Lower,Concat
from django.db.models import Prefetch,Value,CharField, Q, F, Exists, OuterRef, Sum
from django.contrib.sites.shortcuts import get_current_site
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.contrib.auth.mixins import LoginRequiredMixin,PermissionRequiredMixin
from django.utils import timezone
from weasyprint import HTML

from hisensehr.tasks import send_email_to_users
from hisenseapi.helper import notificationUpdate
from hisensehr.helper import encrypt_me, LogUserActivity
from hisensehr.models import CREATE, GOAL, PENDING,  UPDATE, SUCCESS, FAILED, DELETE, READ, Dynamic_menus, Group_menu_access, KPIApprovalStatus, KPILevelPermissions, KpiCategory, KpiGoal, KpiTimePeriod, KpiUserGoal, KpiUserTimePeriod, KpiUserYear, KpiYear, MeasurementType
from ..helper import renderfile,flush_cache,is_ajax,paginationhelper,date_range
from hisensehr.models import Department,User,KpiAction,KpiActionPermission,User_menu_access
from hisensehr.constantvariables import KPI_CATEGORY_MENU, KPI_GOAL_MENU, KPI_USER_GUIDELINE, \
                                            PERFORMANCE_CONFIG_MENU, KPI_GOAL_SETTING_MENU, KPI_EMPLOYEE_MENU, PERFORMANCE_MAIN_MENU
from ..constantvariables import GOAL_APPROVE_MENU, KPI_GOAL_MASTER_MENU, PAGINATION_PERPAGE,APPURL

import json
import traceback
from collections import defaultdict
from operator import itemgetter

# Custom mixin that checks both permission and superuser status
class SuperuserOrPermissionRequiredMixin(PermissionRequiredMixin):
    def has_permission(self):
        # Allow access if user is superuser
        if self.request.user.is_superuser:
            return True
        # Otherwise, check the required permission
        return super().has_permission()

# KPI Goal Setting Permission
class KpiSettingPermissionView(PermissionRequiredMixin,View):
    permission_required = 'hisensehr.view_kpiactionpermission'
    def get(self,request,*args, **kwargs):
        data, context = {}, {}
        page = request.GET.get('page', 1)
        user_ids = request.GET.getlist('users_ids[]')
        users = User.objects.select_related('profile').filter(is_active=True,profile__employement_type = 2).exclude(is_superuser=True).order_by(Lower('first_name')).annotate(
                full_name=Concat('first_name',
                    Value(' '),
                    'last_name'
                    ,output_field=CharField()))
        actions = KpiAction.objects.filter(Q(key_action='kpi_settings'), is_active=True, kpi_action_type=2).order_by('id')
        user_permissions = KpiActionPermission.objects.select_related('kpi_action','user','user__profile','assigned_to','department').filter(kpi_action__kpi_action_type=2,kpi_action__key_action='kpi_settings').exclude(user__profile__employement_type=1).annotate(
                        full_name=Concat('user__first_name',
                            Value(' '),
                            'user__last_name'
                            ,output_field=CharField()))\
                        .values('id','full_name','user_id','assigned_to__first_name','assigned_to__last_name','assigned_to_id','kpi_action_id','department_id')

        if user_ids:
            user_permissions = user_permissions.filter(assigned_to_id__in=user_ids)

        context['actions'] = actions
        context['departments'] = Department.objects.filter(is_active=True).order_by('name')
        context['pending_users'] = len(users)-len(user_permissions.distinct('user'))
        context['user_permissions'] = user_permissions

        # Group departments by user_id
        user_departments = defaultdict(list)
        selected_users = []
        for perm in user_permissions:
            user_departments[perm['assigned_to_id']].append(perm['department_id'])
            selected_users.append({
                'id': perm['assigned_to_id'],
                'name': f"{perm['assigned_to__first_name']} {perm['assigned_to__last_name']}"
            })
        # pagination
        selected_users = sorted(selected_users, key=itemgetter('name'))
        selected_users_list = list({user['id']: user for user in selected_users}.values())
        paginator = Paginator(selected_users_list, PAGINATION_PERPAGE)
        try:
            selected_users_obj = paginator.page(page)
        except PageNotAnInteger:
            selected_users_obj = paginator.page(1)
        except EmptyPage:
            selected_users_obj = paginator.page(paginator.num_pages)
        context['selected_departments'] = dict(user_departments)
        context['selected_users'] = selected_users_obj
        context['user_select'] = users
        context['users'] = users
        context['current_page'] = int(page)
        if is_ajax(request):
            data['status'] = True
            data['html'] = loader.render_to_string('hisensehr/kpi/permissions/kpi_setting_permission_ajax.html',context,request=request)
            # data['pagination'] = loader.render_to_string("hisensehr/kpi/permissions/kpi_setting_permission_pagination.html", data=data, request=request)
            data['pagination'] = loader.render_to_string(
                "hisensehr/kpi/permissions/kpi_setting_permission_pagination.html", 
                context, 
                request=request
            )
            return JsonResponse(data)
        return renderfile(request,'kpi/permissions','kpi_setting_permission_view',context)
     
    def post(self,request,*args, **kwargs):
        data = {}
        context = {}
        link = '' 
        msg = ''
        log_msg = ''
        try:
            with transaction.atomic():
                page = request.GET.get('page', 1)
                action_data = request.POST.get('action_data',None)
                action_data = json.loads(action_data)
                user = request.POST.get('user',None)
                department_ids = []
                for action in action_data:
                    department_ids.extend(action['department'])
                dept_names = Department.objects.filter(id__in=department_ids).values_list('name', flat=True)
                dept_names_str = ", ".join(dept_names)
                if KpiActionPermission.objects.select_related('user','kpi_action').filter(assigned_to_id=user,kpi_action__kpi_action_type=2,kpi_action__key_action__in=['kpi_settings','kpi_goal'],department_id__in=department_ids).exists():
                    data['status'] = False
                    data['message'] = "Permission for this user and department already exists"
                else:
                    created_permissions = []
                    with transaction.atomic():
                        for action in action_data:
                            departments = action.get('department', [])
                            assigned_to_id = action.get('user_id')
                            actions = KpiAction.objects.filter(is_active=True,key_action__in=['kpi_settings','kpi_goal']).order_by('id')
                            # dynamic_menu_manage_kpi = KPI_CATEGORY_MENU
                            # dynamic_menu_kpi_goal = KPI_GOAL_MENU
                            dynamic_menu_manage_kpi_goal = KPI_GOAL_MASTER_MENU
                            for department_id in departments:
                                for actn in actions:
                                    perm = KpiActionPermission.objects.create(kpi_action_id=actn.id,assigned_to_id=assigned_to_id,user_id=None,department_id=department_id)
                                    created_permissions.append(perm)
                            # user menu access
                            User_menu_access.objects.update_or_create(user_id=assigned_to_id,  dynamic_menu_id = PERFORMANCE_CONFIG_MENU, defaults={'dynamic_menu_id': PERFORMANCE_CONFIG_MENU}) #performance main menu
                            # for menu_id in [dynamic_menu_manage_kpi, dynamic_menu_kpi_goal]:
                            User_menu_access.objects.update_or_create(
                                user_id=assigned_to_id,
                                dynamic_menu_id=dynamic_menu_manage_kpi_goal,
                                defaults={'dynamic_menu_id': dynamic_menu_manage_kpi_goal}
                            )

                            #notification
                            scheme = request.is_secure() and "https://" or "http://"
                            current_site = get_current_site(request)
                            domainlink=scheme+current_site.domain  
                            
                            assigned_user = User.objects.only('first_name','last_name').get(id=assigned_to_id)
                            this_user = User.objects.only('first_name','last_name').get(id=user)
                            link = domainlink+'/manage-kpi-goal/'
                            msg = f"{request.user.first_name} {request.user.last_name} has assigned you to set KPI & Goal setting permission for the department(s): {dept_names_str}"
                            log_msg = f"Assigned KPI & Goal setting permission to {assigned_user.first_name} {assigned_user.last_name} for department(s): {dept_names_str}"
                            info = {}
                            info['type'] = 'kpi_goal_settings_permission'
                            info['action'] = 'Create'
                            info['action_id'] = perm.id
                            info['url'] = link
                            info['end_user'] = this_user.id
                            notificationUpdate(user_from=request.user,user_to=assigned_user, message=msg, info=info)
                            #email notification
                            mail_subject = 'KPI & Goal Setting Permission'
                            user_name = assigned_user.first_name+" "+assigned_user.last_name
                            content_replace = {"NAME":user_name, "MESSAGE":msg, "LINK":link, "TITLE":"KPI & Goal Setting Permission","YEAR": timezone.now().year}
                            send_email_to_users.delay(mail_subject, 4, content_replace, assigned_user.email)

                            #log entry
                            log_data = {}
                            log_data['module_name']  = 'kpi_goal_settings_permission'
                            log_data['action_type']  = CREATE
                            log_data['log_message']  = log_msg
                            log_data['status']  = SUCCESS
                            log_data['model_object']  = created_permissions
                            log_data['db_data']  = {}
                            log_data['app_visibility']  = True
                            log_data['web_visibility']  = True
                            log_data['error_msg']  = ''
                            log_data['fwd_link']  = '/kpi-permission/'
                            LogUserActivity(request, log_data)

                    flush_cache('cached_menu_user')
                    user_permissions = KpiActionPermission.objects.filter(kpi_action__kpi_action_type=2,kpi_action__key_action='kpi_settings').exclude(user__profile__employement_type=1).select_related('user','kpi_action','assigned_to','profile').annotate(
                        full_name=Concat('user__first_name',
                            Value(' '),
                            'user__last_name'
                            ,output_field=CharField()))\
                        .values('id','full_name','user_id','assigned_to__first_name','assigned_to__last_name','assigned_to_id','kpi_action_id','department_id')

                    users = User.objects.select_related('profile__department').filter(is_active=True,profile__employement_type = 2).exclude(is_superuser=True).order_by(Lower('first_name')).annotate(
                        full_name=Concat('first_name',
                            Value(' '),
                            'last_name'
                            ,output_field=CharField())).prefetch_related(Prefetch('user_kpi_permissions',queryset= KpiActionPermission.objects.select_related('kpi_action').filter(kpi_action__kpi_action_type=2,kpi_action__key_action='kpi_settings')))
                    actions = KpiAction.objects.filter(Q(key_action='kpi_settings'), is_active=True, kpi_action_type=2).order_by('id')
                    context['actions'] = actions
                    context['departments'] = Department.objects.filter(is_active=True).order_by('name')
                     # Group departments by user_id
                    user_departments = defaultdict(list)
                    selected_users = []
                    for perm in user_permissions:
                        user_departments[perm['assigned_to_id']].append(perm['department_id'])
                        selected_users.append({
                            'id': perm['assigned_to_id'],
                            'name': f"{perm['assigned_to__first_name']} {perm['assigned_to__last_name']}"
                        })
                    # pagination
                    selected_users = sorted(selected_users, key=itemgetter('name'))
                    selected_users_list = list({user['id']: user for user in selected_users}.values())
                    paginator = Paginator(selected_users_list, PAGINATION_PERPAGE )
                    try:
                        selected_users_obj = paginator.page(page)
                    except PageNotAnInteger:
                        selected_users_obj = paginator.page(1)
                    except EmptyPage:
                        selected_users_obj = paginator.page(paginator.num_pages)
                    context['selected_departments'] = dict(user_departments)
                    context['selected_users'] = selected_users_obj
                    context['pending_users'] = len(users)-len(user_permissions.distinct('user'))
                    context['user_select'] = users
                    users = paginationhelper(users,page)
                    context['users'] = users
                    context['user_permissions'] = user_permissions.order_by('-id')
                    context['current_page'] = int(page)
                    template = loader.render_to_string('hisensehr/kpi/permissions/kpi_setting_permission_ajax.html',context=context,request=request)
                    pagination = loader.render_to_string('hisensehr/kpi/permissions/pagination.html',context=context,request=request)
                    data['template'] = template
                    data['pagination'] = pagination
                    data['status'] = True
        except Exception as e:
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_goal_settings_permission'
            log_data['action_type']  = CREATE
            log_data['log_message']  = 'KPI & Goal setting permission failed'
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = traceback.format_exc()
            log_data['fwd_link']  = '/kpi-permission/'
            LogUserActivity(request, log_data)
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class KpiSettingPermissionDeleteView(PermissionRequiredMixin,View):
    permission_required = 'hisensehr.delete_kpiactionpermission'
    def post(self,request,*args, **kwargs):        
        data = {}
        context = {}
        try:
            user = request.POST.get('user_id')
            user_data = User.objects.get(id=user)
            if KpiActionPermission.objects.filter(assigned_to_id=user).exists():
                with transaction.atomic():
                    KpiActionPermission.objects.filter(
                        assigned_to_id=user,
                        kpi_action__kpi_action_type=2,
                        kpi_action__key_action__in=['kpi_settings', 'kpi_goal']
                    ).delete()
                    msg = f"Revoked {user_data.first_name} {user_data.last_name}'s KPI setting permission."
                    #log entry                      
                    log_data = {}
                    log_data['module_name']  = 'kpi_setting_permission'
                    log_data['action_type']  = DELETE
                    log_data['log_message']  = msg
                    log_data['status']  = SUCCESS
                    log_data['model_object']  = None
                    log_data['db_data']  = {}
                    log_data['app_visibility']  = True
                    log_data['web_visibility']  = True
                    log_data['error_msg']  = ''
                    log_data['fwd_link']  = ''
                    LogUserActivity(request, log_data)
                    has_other_kpi_permissions = KpiActionPermission.objects.filter(assigned_to_id=user).exists()
                    if not has_other_kpi_permissions:
                        User_menu_access.objects.filter(user_id=user, dynamic_menu_id__in = [KPI_GOAL_MENU,KPI_CATEGORY_MENU,PERFORMANCE_CONFIG_MENU,KPI_GOAL_MASTER_MENU]).delete() 
            context['users'] = User.objects.select_related('profile__department').filter(is_active=True,profile__employement_type = 2).exclude(is_superuser=True).order_by(Lower('first_name')).annotate(
                        full_name=Concat('first_name',
                            Value(' '),
                            'last_name'
                            ,output_field=CharField())).prefetch_related(Prefetch('user_kpi_permissions',queryset= KpiActionPermission.objects.select_related('kpi_action').filter(kpi_action__kpi_action_type=2,kpi_action__key_action='kpi_settings')))
            context['actions'] = KpiAction.objects.filter(Q(key_action='kpi_settings'), is_active=True, kpi_action_type=2).order_by('id')
            template = loader.render_to_string('hisensehr/kpi/permissions/kpi_setting_permission_ajax.html',context=context,request=request)
            data['template'] = template
            data['status'] = True           
        except Exception as e:
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_setting_permission'
            log_data['action_type']  = DELETE
            log_data['log_message']  = 'KPI setting permission revoking failed'
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = traceback.format_exc()
            log_data['fwd_link']  = '/kpi-setting-permission-delete/'
            LogUserActivity(request, log_data)
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

class KpiSettingPermissionEditView(PermissionRequiredMixin,View):
    permission_required = 'hisensehr.change_kpiactionpermission'

    def post(self,request,*args, **kwargs):       
        data, context = {}, {} 
        try:
            # dynamic_menu_manage_kpi = KPI_CATEGORY_MENU
            # dynamic_menu_kpi_goal = KPI_GOAL_MENU
            dynamic_menu_kpi_goal_master = KPI_GOAL_MASTER_MENU
            performance_menu_id = PERFORMANCE_CONFIG_MENU
            user_id = request.POST.get('user')
            page = request.POST.get('page', 1)
            department_list = request.POST.getlist('department[]')

            # Handle "Select All" case
            if 'all' in department_list:
                # Get all active department IDs
                new_department_ids = set(Department.objects.filter(is_active=True).values_list('id', flat=True))
            else:
                # Convert selected department IDs to integers
                new_department_ids = set(map(int, department_list))

            current_site = get_current_site(request)
            scheme = "https://" if request.is_secure() else "http://"
            domain_link = f"{scheme}{current_site.domain}"
            kpi_goal_master_link = f"{domain_link}/manage-kpi-goal/"
            end_user = User.objects.only('first_name', 'last_name').get(id=user_id)

            if KpiActionPermission.objects.filter(assigned_to_id=user_id).exists():
                with transaction.atomic():
                    existing_dept_ids = set(KpiActionPermission.objects.filter(assigned_to_id=user_id).values_list('department_id', flat=True))
                    departments_to_add = new_department_ids - existing_dept_ids
                    departments_to_remove = existing_dept_ids - new_department_ids
                    # Remove permissions for departments that are no longer selected
                    if departments_to_remove:
                        removed_departments = list(Department.objects.filter(id__in=departments_to_remove, is_active=True).values_list('name', flat=True))
                        dept_names = ', '.join(removed_departments)
                        KpiActionPermission.objects.filter(
                            assigned_to_id=user_id,
                            department_id__in=departments_to_remove,
                            kpi_action__kpi_action_type=2,
                            kpi_action__key_action__in=['kpi_settings', 'kpi_goal']
                        ).delete()
                        # Check if user has any remaining KPI permissions
                        has_other_permissions = KpiActionPermission.objects.filter(assigned_to_id=user_id).exists()
                        if not has_other_permissions:
                            # Remove UserMenuAccess entries if all permissions removed
                            User_menu_access.objects.filter(user_id=user_id, dynamic_menu_id__in=[
                                performance_menu_id, dynamic_menu_kpi_goal_master, #dynamic_menu_manage_kpi, dynamic_menu_kpi_goal
                            ]).delete()

                        message = f"{request.user.first_name} {request.user.last_name} has revoked your KPI & Goal setting permissions for department(s): {dept_names}."
                        log_message = f"Revoked KPI & Goal setting permission of {end_user.first_name} {end_user.last_name} for department(s): {dept_names}."
                        # Notification
                        info = {
                            'type': 'kpi_goal_settings_permission',
                            'action': 'Delete',
                            'action_id': None,
                            'url': kpi_goal_master_link,
                            'end_user': end_user.id,
                        }
                        notificationUpdate(user_from=request.user, user_to=end_user, message=message, info=info)
                        # Email notification
                        mail_subject = 'KPI & Goal Setting Permission Removed'
                        user_name = f"{end_user.first_name} {end_user.last_name}"
                        content_replace = {"NAME": user_name, "MESSAGE": message, "LINK": kpi_goal_master_link, "TITLE": "KPI & Goal Setting Permission Removed", "YEAR": timezone.now().year}
                        send_email_to_users.delay(mail_subject, 4, content_replace, end_user.email)

                        # Log removal
                        log_data = {}
                        log_data['module_name']  = 'kpi_goal_settings_permission'
                        log_data['action_type']  = DELETE
                        log_data['log_message']  = log_message
                        log_data['status']  = SUCCESS
                        log_data['model_object']  = None
                        log_data['db_data']  = {}
                        log_data['app_visibility']  = True
                        log_data['web_visibility']  = True
                        log_data['error_msg']  = ''
                        log_data['fwd_link']  = '/kpi-permission/'
                        LogUserActivity(request, log_data)
                    # Add permissions for new departments
                    if departments_to_add:
                        added_departments = list(Department.objects.filter(id__in=departments_to_add, is_active=True).values_list('name', flat=True))
                        added_dept_names = ', '.join(added_departments)
                        created_permissions = []
                        kpi_actions = KpiAction.objects.filter(kpi_action_type=2,key_action__in=['kpi_settings', 'kpi_goal'])
                        for dept_id in departments_to_add:
                            for action in kpi_actions:
                                perm = KpiActionPermission.objects.create(kpi_action=action,assigned_to_id=user_id,department_id=dept_id)
                                created_permissions.append(perm)
                            # user menu access
                            User_menu_access.objects.update_or_create(user_id=user_id,  dynamic_menu_id=performance_menu_id, defaults={'dynamic_menu_id': performance_menu_id})
                            # for menu_id in [dynamic_menu_manage_kpi, dynamic_menu_kpi_goal, dynamic_menu_kpi_goal_master]:
                            User_menu_access.objects.get_or_create(user_id=user_id, dynamic_menu_id=dynamic_menu_kpi_goal_master)
                            #notification
                            message = f"{request.user.first_name} {request.user.last_name} has assigned you to set KPI & Goal setting permission for the department(s): {added_dept_names}"
                            log_message = f"Assigned KPI & Goal setting permission to {end_user.first_name} {end_user.last_name} for department(s): {added_dept_names}"
                            info = {}
                            info['type'] = 'kpi_goal_settings_permission'
                            info['action'] = 'Create'
                            info['action_id'] = perm.id
                            info['url'] = kpi_goal_master_link
                            info['end_user'] = end_user.id
                            notificationUpdate(user_from=request.user,user_to=end_user,message=message, info=info)
                            #email notification
                            mail_subject = 'KPI & Goal Setting Permission'
                            user_name = f"{end_user.first_name} {end_user.last_name}",
                            content_replace = {"NAME":user_name, "MESSAGE":message, "LINK":kpi_goal_master_link, "TITLE":"KPI & Goal Setting Permission","YEAR": timezone.now().year}
                            send_email_to_users.delay(mail_subject, 4, content_replace, end_user.email)

                            #log entry
                            log_data = {}
                            log_data['module_name']  = 'kpi_goal_settings_permission'
                            log_data['action_type']  = CREATE
                            log_data['log_message']  = log_message
                            log_data['status']  = SUCCESS
                            log_data['model_object']  = created_permissions
                            log_data['db_data']  = {}
                            log_data['app_visibility']  = True
                            log_data['web_visibility']  = True
                            log_data['error_msg']  = ''
                            log_data['fwd_link']  = '/kpi-permission/'
                            LogUserActivity(request, log_data)
                    flush_cache('cached_menu_user')
                    # retrieve updated permissions   
                    user_permissions = KpiActionPermission.objects.filter(kpi_action__kpi_action_type=2,kpi_action__key_action='kpi_settings').exclude(user__profile__employement_type=1).select_related('user','kpi_action','assigned_to','profile').annotate(
                        full_name=Concat('user__first_name',
                            Value(' '),
                            'user__last_name'
                            ,output_field=CharField()))\
                        .values('id','full_name','user_id','assigned_to__first_name','assigned_to__last_name','assigned_to_id','kpi_action_id','department_id')

                    users = User.objects.select_related('profile__department').filter(is_active=True,profile__employement_type = 2).exclude(is_superuser=True).order_by(Lower('first_name')).annotate(
                        full_name=Concat('first_name',
                            Value(' '),
                            'last_name'
                            ,output_field=CharField())).prefetch_related(Prefetch('user_kpi_permissions',queryset= KpiActionPermission.objects.select_related('kpi_action').filter(kpi_action__kpi_action_type=2,kpi_action__key_action='kpi_settings')))
                    actions = KpiAction.objects.filter(Q(key_action='kpi_settings'), is_active=True, kpi_action_type=2).order_by('id')
                    context['actions'] = actions
                    context['departments'] = Department.objects.filter(is_active=True).order_by('name')
                    # Group departments by user_id
                    user_departments = defaultdict(list)
                    selected_users = []
                    for perm in user_permissions:
                        user_departments[perm['assigned_to_id']].append(perm['department_id'])
                        selected_users.append({
                            'id': perm['assigned_to_id'],
                            'name': f"{perm['assigned_to__first_name']} {perm['assigned_to__last_name']}"
                        })
                    # pagination
                    selected_users = sorted(selected_users, key=itemgetter('name'))
                    selected_users_list = list({user['id']: user for user in selected_users}.values())
                    paginator = Paginator(selected_users_list, PAGINATION_PERPAGE )
                    try:
                        selected_users_obj = paginator.page(page)
                    except PageNotAnInteger:
                        selected_users_obj = paginator.page(1)
                    except EmptyPage:
                        selected_users_obj = paginator.page(paginator.num_pages)
                    context['selected_departments'] = dict(user_departments)
                    context['selected_users'] = selected_users_obj
                    context['pending_users'] = len(users)-len(user_permissions.distinct('user'))
                    context['user_select'] = users
                    # users = paginationhelper(users,page)
                    context['users'] = users
                    context['current_page'] = int(page)
                    context['user_permissions'] = user_permissions.order_by('-id')
                    template = loader.render_to_string('hisensehr/kpi/permissions/kpi_setting_permission_ajax.html',context=context,request=request)
                    pagination = loader.render_to_string('hisensehr/kpi/permissions/pagination.html',context=context,request=request)
                    data['template'] = template
                    data['pagination'] = pagination
                    data['status'] = True
        except Exception as e:
            print("----",e)
            #log entry
            log_data = {}
            log_data['module_name']  = 'kpi_goal_settings_permission'
            log_data['action_type']  = UPDATE
            log_data['log_message']  = 'KPI & Goal setting permission failed'
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = traceback.format_exc()
            log_data['fwd_link']  = '/kpi-permission/'
            LogUserActivity(request, log_data)
            data['status'] = False
            data['message'] = "Something went wrong"
        return JsonResponse(data)

# KPI GOAL MASTER   
class ManageKPIGoals(SuperuserOrPermissionRequiredMixin,View):
    # permission_required = 'hisensehr.view_kpicategory'

    def has_permission(self):
        # Check default permission first
        user = self.request.user
        if not user or not user.is_authenticated:
            return False

        # check if the user has access to the dynamic menu
        menu = Dynamic_menus.objects.filter(title_slug='manage_kpi_goal',is_active=True).last()
        if menu:
            menu_id = menu.id
            has_access = User_menu_access.objects.filter(user=user, dynamic_menu_id=menu_id).exists()
            if has_access:
                return True
            user_groups = user.groups.values_list('id', flat=True)
            if user_groups:
                group_access = Group_menu_access.objects.filter(group_id__in=user_groups, dynamic_menu_id=menu_id).exists()
                return group_access
            else:
                return False
        else:
            return False
    
    def get(self,request,*args, **kwargs):
        data, context = {}, {}
        dept_id = request.GET.get('department_id')
        measurement_types = MeasurementType.objects.filter(is_active=True)
        kpi_categories = KpiCategory.objects.filter(department_id=dept_id,is_active=True).prefetch_related(
            Prefetch(
                'kpi_goals',
                queryset=KpiGoal.objects.filter(is_active=True).select_related(
                    'measurement_type'
                )
            )
        )
        kpi_data = []
        for category in kpi_categories:
            for goal in category.kpi_goals.all():
                kpi_data.append({
                    'goal_id': goal.id,
                    'goal_name': goal.name,
                    'kpi_id': category.id,
                    'kpi_category_name': category.name,
                    'measurement_id': goal.measurement_type_id,
                })
        context['measurement_types'] = measurement_types
        context['kpi_data'] = kpi_data
        if is_ajax(request):
            data['status'] = True
            data['template'] = loader.render_to_string('hisensehr/kpi/kpi_goal_master/manage_kpi_goal_ajax.html',context,request=request)
            return JsonResponse(data)
        assigned_dept_ids = KpiActionPermission.objects.filter(assigned_to=request.user).values_list('department_id', flat=True).distinct()
        context['departments'] = Department.objects.filter(id__in=assigned_dept_ids,is_active=True).order_by('name')
        return renderfile(request,'kpi/kpi_goal_master','manage_kpi_goal',context)
    
    def post(self,request,*args, **kwargs):
        context, data = {}, {}
        try:
            with transaction.atomic():
                kpi_category = request.POST.get('kpi_category').strip()
                kpi_goal = request.POST.get('kpi_goal').strip()
                measurement_type_id = request.POST.get('measurement_type')
                department_id = request.POST.get('department_id')
                measurement_types = MeasurementType.objects.filter(is_active=True)
                # Check for same KPI + same Goal in same department
                existing_combination = KpiGoal.objects.filter(
                    is_active=True,
                    name__iexact=kpi_goal,
                    kpi_category__name__iexact=kpi_category,
                    kpi_category__department_id=department_id
                ).exists()

                if existing_combination:
                    data['status'] = False
                    data['message'] = 'This KPI and Goal combination already exists in this department.'
                    return JsonResponse(data)

                # Always create new KPI Category (each goal gets its own KPI entry)
                kpi_master = KpiCategory.objects.create(
                    name=kpi_category,
                    department_id=department_id,
                    is_active=True
                )

                # Create new Goal under the new KPI
                goal_master = KpiGoal.objects.create(
                    name=kpi_goal,
                    kpi_category=kpi_master,
                    measurement_type_id=measurement_type_id,
                    is_active=True,
                    added_by=request.user
                )

                #log entry
                log_data = {}
                log_data['module_name']  = 'manage_kpi_goal'
                log_data['action_type']  = CREATE
                log_data['log_message']  = 'KPI & Goal created'
                log_data['status']  = SUCCESS
                log_data['model_object']  = kpi_master
                log_data['db_data']  = {'kpi_record':kpi_master.id,'goal_record':goal_master.id,'department_id':department_id}
                log_data['app_visibility']  = True
                log_data['web_visibility']  = True
                log_data['error_msg']  = ''
                log_data['fwd_link']  = '/manage-kpi-goal/'
                LogUserActivity(request, log_data)
        
                # Fetch updated KPI data
                kpi_data = []
                kpi_categories = KpiCategory.objects.filter(department_id=department_id, is_active=True)
                for category in kpi_categories:
                    goals = KpiGoal.objects.filter(kpi_category=category, is_active=True)
                    for goal in goals:
                        kpi_data.append({
                        'goal_id': goal.id,
                        'goal_name': goal.name,
                        'kpi_id': category.id,
                        'kpi_category_name': category.name,
                        'measurement_id': goal.measurement_type_id,
                    })
                context['measurement_types'] = measurement_types
                context['kpi_data'] = kpi_data
                data['status'] = True
                data['template'] = loader.render_to_string('hisensehr/kpi/kpi_goal_master/manage_kpi_goal_ajax.html',context,request=request)
                return JsonResponse(data)
        except Exception as e:
            #log entry
            log_data = {}
            log_data['module_name']  = 'manage_kpi_goal'
            log_data['action_type']  = CREATE
            log_data['log_message']  = 'KPI & Goal creation failed'
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = traceback.format_exc()
            log_data['fwd_link']  = '/manage-kpi-goal/'
            LogUserActivity(request, log_data)
            return JsonResponse({'status': False, 'message': str(e)})
        
class ManageKPIGoalsDeleteView(SuperuserOrPermissionRequiredMixin,View):
    def has_permission(self):
        # Check default permission first
        user = self.request.user
        if not user or not user.is_authenticated:
            return False

        # check if the user has access to the dynamic menu
        menu = Dynamic_menus.objects.filter(title_slug='manage_kpi_goal',is_active=True).last()
        if menu:
            menu_id = menu.id
            has_access = User_menu_access.objects.filter(user=user, dynamic_menu_id=menu_id).exists()
            if has_access:
                return True
            user_groups = user.groups.values_list('id', flat=True)
            if user_groups:
                group_access = Group_menu_access.objects.filter(group_id__in=user_groups, dynamic_menu_id=menu_id).exists()
                return group_access
            else:
                return False
        else:
            return False
    
    def post(self, request, *args, **kwargs):
        data = {}
        try:
            with transaction.atomic():
                kpi_id = request.POST.get('kpi_id')
                goal_id = request.POST.get('goal_id')
                # Check if assigned to any user
                kpi_user_goal = KpiUserGoal.objects.select_related('kpi_goal').filter(kpi_goal_id=goal_id,is_active=True)
                if kpi_user_goal.exists():
                    approval_pending = KpiUserYear.objects.filter(id=kpi_user_goal.last().kpi_user_year.id,user_id=kpi_user_goal.last().kpi_user_year.user_id,status__in=[1,2]).exists()
                    if approval_pending:
                        return JsonResponse({
                            'status': False,
                            'message': 'This KPI & Goal is already assigned to users. Please either approve or delete the assigned goals before deleting it.'
                        })
                kpi_category = KpiCategory.objects.filter(id=kpi_id, is_active=True).first()
                if not kpi_category:
                    data['status'] = False
                    data['message'] = "KPI not found or already deleted."
                    return JsonResponse(data)
                kpi_category.is_active = False
                kpi_category.save()
                related_goals = KpiGoal.objects.filter(kpi_category=kpi_category, is_active=True).last()
                related_goals.is_active = False
                related_goals.save()
                data_log = {'kpi_id': kpi_id,'kpi_name': kpi_category.name,'goal_id': goal_id,'goal_name': related_goals.name}

                # Log the deletion
                log_data = {}
                log_data['module_name'] = 'manage_kpi_goal_delete'
                log_data['action_type'] = DELETE
                log_data['log_message'] = f'KPI "{kpi_category.name}" and its associated goals deleted'
                log_data['status'] = SUCCESS
                log_data['model_object'] = kpi_category
                log_data['db_data'] = data_log
                log_data['app_visibility'] = True
                log_data['web_visibility'] = True
                log_data['error_msg'] = ''
                log_data['fwd_link'] = '/manage-kpi-goal/'
                LogUserActivity(request, log_data)
                
                data['status'] = True
                data['message'] = "KPI and goal have been deleted successfully."
        except Exception as e:
            #log entry
            log_data = {}
            log_data['module_name']  = 'manage_kpi_goal_delete'
            log_data['action_type']  = DELETE
            log_data['log_message']  = 'KPI & Goal deletion failed'
            log_data['status']  = FAILED
            log_data['model_object']  = None
            log_data['db_data']  = {}
            log_data['app_visibility']  = False
            log_data['web_visibility']  = False
            log_data['error_msg']  = traceback.format_exc()
            log_data['fwd_link']  = '/manage-kpi-goal/'
            LogUserActivity(request, log_data)
            data['status'] = False
            data['message'] = "An error occurred while deleting the KPI Goal."
        return JsonResponse(data)

class CheckKPIGoalAssignmentView(SuperuserOrPermissionRequiredMixin,View):
    def has_permission(self):
        # Check default permission first
        user = self.request.user
        if not user or not user.is_authenticated:
            return False

        # check if the user has access to the dynamic menu
        menu = Dynamic_menus.objects.filter(title_slug='manage_kpi_goal',is_active=True).last()
        if menu:
            menu_id = menu.id
            has_access = User_menu_access.objects.filter(user=user, dynamic_menu_id=menu_id).exists()
            if has_access:
                return True
            user_groups = user.groups.values_list('id', flat=True)
            if user_groups:
                group_access = Group_menu_access.objects.filter(group_id__in=user_groups, dynamic_menu_id=menu_id).exists()
                return group_access
            else:
                return False
        else:
            return False

    def post(self, request, *args, **kwargs):
        data = {}
        try:
            goal_id = request.POST.get('goal_id')

            # Check if assigned to any user
            kpi_user_goal = KpiUserGoal.objects.select_related('kpi_goal').filter(kpi_goal_id=goal_id,is_active=True)
            if kpi_user_goal.exists():
                # Get user names who have this goal assigned
                assigned_users = []
                for user_goal in kpi_user_goal:
                    user_name = f"{user_goal.kpi_user_year.user.first_name} {user_goal.kpi_user_year.user.last_name}"
                    assigned_users.append(user_name)

                data['status'] = True
                data['is_assigned'] = True
                data['assigned_users'] = assigned_users[:5]  # Show first 5 users
                data['total_users'] = kpi_user_goal.count()
            else:
                data['status'] = True
                data['is_assigned'] = False

        except Exception as e:
            data['status'] = False
            data['message'] = "Error checking assignment status"

        return JsonResponse(data)

class ManageKPIEditView(SuperuserOrPermissionRequiredMixin,View):
    def has_permission(self):
        # Check default permission first
        user = self.request.user
        if not user or not user.is_authenticated:
            return False

        # check if the user has access to the dynamic menu
        menu = Dynamic_menus.objects.filter(title_slug='manage_kpi_goal',is_active=True).last()
        if menu:
            menu_id = menu.id
            has_access = User_menu_access.objects.filter(user=user, dynamic_menu_id=menu_id).exists()
            if has_access:
                return True
            user_groups = user.groups.values_list('id', flat=True)
            if user_groups:
                group_access = Group_menu_access.objects.filter(group_id__in=user_groups, dynamic_menu_id=menu_id).exists()
                return group_access
            else:
                return False
        else:
            return False
    
    def post(self,request,*args, **kwargs):        
        try:
            with transaction.atomic():
                kpi_id = request.POST.get("kpi_id")
                goal_id = request.POST.get("goal_id")
                kpi_name = request.POST.get("kpi_name", "").strip()
                goal_name = request.POST.get("goal_name", "").strip()
                measurement_type = request.POST.get("measurement_type", "").strip()
                kpi_name_old = request.POST.get("kpi_name_old", "").strip()
                goal_name_old = request.POST.get("goal_name_old", "").strip()

                if not kpi_name:
                    return JsonResponse({"status": False, "message": "KPI name should not be empty"})
                if not goal_name:
                    return JsonResponse({"status": False, "message": "Goal name should not be empty"})
                if not measurement_type:
                    return JsonResponse({"status": False, "message": "Measurement type should not be empty"})
                
                # Update KPI
                kpi = KpiCategory.objects.filter(id=kpi_id, is_active=True).first()
                # if not kpi:
                #     old_kpi = KpiGoal.objects.filter(kpi_category__name__iexact=kpi_name, name__iexact=goal_name, is_active=True).first()
                #     if old_kpi:
                #         kpi = old_kpi.kpi_category
                #     else:
                #         old_kpi = KpiGoal.objects.filter(kpi_category__name__iexact=kpi_name_old, name__iexact=goal_name_old, is_active=True).first()
                #         kpi = old_kpi.kpi_category

                # Get current KPI and Goal
                if not kpi:
                    return JsonResponse({"status": False, "message": "KPI not found."})

                goal = KpiGoal.objects.filter(id=goal_id, is_active=True).first()
                # if not goal:
                #   
                #     old_goal = KpiGoal.objects.filter(kpi_category__name__iexact=kpi_name, name__iexact=goal_name, is_active=True).first()
                #     if old_goal:
                #         goal = old_goal
                #     else:
                #         old_goal = KpiGoal.objects.filter(kpi_category__name__iexact=kpi_name_old, name__iexact=goal_name_old, is_active=True).first()
                #         goal = old_goal

                if not goal:
                    return JsonResponse({"status": False, "message": "Goal not found."})

                # Check if KPI or Goal name has changed
                kpi_name_changed = kpi.name.lower() != kpi_name.lower()
                goal_name_changed = goal.name.lower() != goal_name.lower()

                if kpi_name_changed or goal_name_changed:
                    # Check for same KPI + same Goal combination in same department
                    existing_combination = KpiGoal.objects.filter(
                        is_active=True,
                        name__iexact=goal_name,
                        kpi_category__name__iexact=kpi_name,
                        kpi_category__department_id=kpi.department_id
                    ).exclude(id=goal_id).exists()

                    if existing_combination:
                        return JsonResponse({
                            "status": False,
                            "message": "This KPI and Goal combination already exists in this department."
                        })

                    # Update existing KPI category name
                    kpi.name = kpi_name
                    kpi.save()

                    # Update existing Goal
                    goal.name = goal_name
                    goal.measurement_type_id = measurement_type
                    goal.save()

                    new_kpi = kpi
                    new_goal = goal
                else:
                    # Only measurement type changed, update in place
                    goal.measurement_type_id = measurement_type
                    goal.save()

                # Log entry
                log_data = {}
                log_data['module_name'] = 'manage_kpi_goal_edit'
                log_data['action_type'] = UPDATE
                log_data['log_message'] = f'KPI "{kpi.name}" updated with Goal "{goal.name}" and Measurement Type ID "{measurement_type}".'
                log_data['status'] = SUCCESS
                log_data['model_object'] = kpi
                log_data['db_data'] = {
                        'kpi_id': kpi_id,
                        'goal_id': goal_id,
                        'measurement_type_id': measurement_type
                    }
                log_data['app_visibility'] = True
                log_data['web_visibility'] = True
                log_data['error_msg'] = ''
                log_data['fwd_link'] = '/manage-kpi-goal/'
                LogUserActivity(request, log_data)

                return JsonResponse({"status": True, "message": "Updated successfully."})
        except Exception as e:
            log_data = {}
            log_data['module_name'] = 'manage_kpi_goal_edit'
            log_data['action_type'] = UPDATE
            log_data['log_message'] = 'Failed to update KPI and Goal.'
            log_data['status'] = FAILED
            log_data['model_object'] = None
            log_data['db_data'] = {
                'kpi_id': kpi_id,
                'goal_id': goal_id,
                'measurement_type_id': measurement_type
            }
            log_data['app_visibility'] = False
            log_data['web_visibility'] = False
            log_data['error_msg'] = traceback.format_exc()
            log_data['fwd_link'] = '/manage-kpi-goal/'
            LogUserActivity(request, log_data)
            return JsonResponse({"status": False, "message": str(e)})


# assign kpi and goals to a employee
class AssignKPIGoalsView(LoginRequiredMixin,View):
    login_url = '/'
    def post(self,request,*args, **kwargs):
        data = {}
        try:
            with transaction.atomic():
                action = request.POST.get('action')
                user_id = request.POST.get('user_id')
                period_id = request.POST.get('period_id')
                department_id = request.POST.get('department_id')
                weight = request.POST.get('weight')
                max_finish_rate = request.POST.get('max_finish_rate')
                goal_value = request.POST.get('goal_value')
                measurement_type = request.POST.get('measurement_type')
                kpi_name = request.POST.get('kpi_name')
                goal_name = request.POST.get('goal_name')
                action = request.POST.get('action')
                category_id = request.POST.get('kpi_category_id')
                if action == "add_row":
                    # Check if the same goal and KPI already exist for this employee
                    existing_goal = KpiUserGoal.objects.filter(
                        kpi_user_year__user_id=user_id,
                        kpi_goal__name=goal_name,
                        kpi_goal__kpi_category__name=kpi_name,
                        kpi_user_year__kpi_year__current_year=True
                    ).exists()

                    if existing_goal:
                        return JsonResponse({
                            'status': False,
                            'message': 'The same goal and KPI already exist for this employee.'
                        })

                    existing_category_goal = KpiGoal.objects.filter(
                        name__iexact=goal_name,
                        kpi_category__name__iexact=kpi_name,
                        kpi_category__department_id=department_id,
                        is_active=True
                    )
                   

                    if not existing_category_goal.exists():
                        # Always create new KPI category (each goal gets its own KPI entry)
                        kpi = KpiCategory.objects.create(name=kpi_name, department_id=department_id, is_active=True)
                        kpi_created = True

                        # Always create new Goal under the new KPI
                        goal = KpiGoal.objects.create(name=goal_name, kpi_category=kpi, is_active=True, measurement_type_id=measurement_type, added_by=request.user)
                        goal_created = True
                    else:
                        kpi = existing_category_goal.first().kpi_category
                        goal = existing_category_goal.first()
                        kpi_created = False
                        goal_created = False

                    user_year, created = KpiUserYear.objects.update_or_create(
                        kpi_year_id=period_id,
                        user_id=user_id,
                        defaults={'status': 1}  # Pending status
                    )

                    KpiUserGoal.objects.create(kpi_user_year=user_year, kpi_goal=goal, goal_value=goal_value, max_finish_rate=max_finish_rate, weight=weight, added_by=request.user, is_active=False)

                    # log entry
                    log_data = {}
                    log_data['module_name'] = 'assign_kpi_goals'
                    log_data['action_type'] = CREATE
                    log_data['log_message'] = f'KPI "{kpi.name}" and Goal "{goal.name}" added for user ID {user_id}.',
                    log_data['status'] = SUCCESS
                    log_data['model_object'] = kpi
                    log_data['db_data'] = {
                        'kpi_id': kpi.id,
                        'goal_id': goal.id,
                        'user_id': user_id,
                        'department_id': department_id
                    }
                    log_data['app_visibility'] = True
                    log_data['web_visibility'] = True
                    log_data['error_msg'] = ''
                    log_data['fwd_link'] = '/assign-kpi-goal/'
                    LogUserActivity(request, log_data)
                    
                    data['status'] = True
                    data['message'] = "KPI and Goal added successfully."
                    data['kpi'] = {'id': kpi.id,'name': kpi.name,'created': kpi_created}
                    data['goal'] = {'id': goal.id,'name': goal.name,'created': goal_created }

                    context = {}
                    context['userid'] = user_id
                    context['action'] = action
                    context['period'] = period_id
                    user_records = User.objects.get(id=user_id)
                    categories = KpiCategory.objects.filter(is_active=True,department_id=user_records.profile.department.id)
                    categories = categories.prefetch_related(
                            Prefetch(
                                'kpi_goals',
                                queryset=KpiGoal.objects.filter(is_active=True).annotate(
                                    has_user_goal=Exists(
                                        KpiUserGoal.objects.filter(kpi_goal=OuterRef('id'),kpi_user_year=user_year)
                                    )
                                ).filter(has_user_goal=True).select_related('measurement_type').annotate(
                                    user_goal_id=F('kpiusergoal__id'),
                                    goal_value=F('kpiusergoal__goal_value'),
                                    max_finish_rate=F('kpiusergoal__max_finish_rate'),
                                    weight=F('kpiusergoal__weight')
                                ).order_by('-id')
                            )
                        ).order_by("department", "id")

                    data_exist_flag = False
                    for category in categories:
                        if len(category.kpi_goals.all()) > 0:
                            data_exist_flag = True
                            break
                    
                    context['categories'] = categories
                    context['data_exist_flag'] = data_exist_flag
                    context['measurement_types'] = MeasurementType.objects.filter(is_active=True)
                    template = loader.render_to_string('hisensehr/kpi/goalset/assign_kpi_goal_values_ajax.html', context=context, request=request)
                    data['template'] = template
                    data['status'] = True
                    data['message'] = "KPI and Goal successfully assigned"
                    return JsonResponse(data)
                    
                elif action == "save_all":  
                    goal_data_list = json.loads(request.POST.get('goal_data_list', '[]'))
                    total_weight = 0
                    valid_goal_data_list = []
                    # Validate total weight
                    period_id = None
                    department_id = None
                    user_id = None
                    for item in goal_data_list:
                        goal_name = item.get('goal_name', '').strip()
                        kpi_name = item.get('kpi_name', '').strip()
                        weight = item.get('weight')
                        period_id = item.get('period_id')
                        department_id = item.get('department_id')
                        user_id = item.get('user_id')
                        if not goal_name or not kpi_name or weight is None or weight == '':
                            continue
                        total_weight += weight
                        valid_goal_data_list.append(item)
                    if total_weight != 100:
                        data['status'] = False
                        data['message'] = "Total weight should be 100%."
                        return JsonResponse(data)

                    if KpiUserYear.objects.select_related('kpi_year','user').filter(kpi_year_id=period_id,user_id=user_id,status=2).exists():
                        data['status'] = False
                        data['message'] = "Goals are either active or approved and cannot be edited at this stage."
                    else:
                        user_year, created = KpiUserYear.objects.update_or_create(
                            kpi_year_id=period_id,
                            user_id=user_id,
                            defaults={'status': 1}  # Pending status
                        )
                        KpiUserGoal.objects.filter(kpi_user_year_id=period_id,kpi_user_year__kpi_year__current_year=True).update(is_active=False)
                        edit_flag = 0
                        for goal_vals in valid_goal_data_list:
                            goal_id = goal_vals.get('goal_id')
                            kpi_name = goal_vals.get('kpi_name').strip()
                            goal_name = goal_vals.get('goal_name').strip()

                            # Check if the goal already exists
                            goal = KpiGoal.objects.filter(id=goal_id).select_related('kpi_category').first()

                            if goal:
                                # Update the existing KPI and Goal
                                kpi = goal.kpi_category

                                # Check if the new KPI name conflicts with other KPIs in the same department
                                if kpi.name != kpi_name:
                                    if KpiCategory.objects.filter(name=kpi_name, department_id=department_id).exclude(id=kpi.id).exists():
                                        data['status'] = False
                                        data['message'] = f"KPI name '{kpi_name}' already exists in the department."
                                        return JsonResponse(data)

                                    # Update the KPI name
                                    kpi.name = kpi_name
                                    kpi.save()

                                # Update the Goal
                                goal.name = goal_name
                                measurement_type_id = goal_vals.get('measurement_type')

                                # Check if measurement_type_id is null (deleted goal)
                                if not measurement_type_id:
                                    data['status'] = False
                                    data['message'] = "Some goals have been deleted and need to be removed from the list before activation. Please remove the deleted goals and try again."
                                    return JsonResponse(data)

                                goal.measurement_type_id = measurement_type_id
                                goal.is_active = True
                                goal.save()
                            else:
                                # Create a new KPI and Goal
                                measurement_type_id = goal_vals.get('measurement_type')

                                # Check if measurement_type_id is null (deleted goal)
                                if not measurement_type_id:
                                    data['status'] = False
                                    data['message'] = "Some goals have been deleted and need to be removed from the list before activation. Please remove the deleted goals and try again."
                                    return JsonResponse(data)

                                kpi, _ = KpiCategory.objects.update_or_create(
                                    name=kpi_name,
                                    department_id=department_id,
                                    defaults={'is_active': True}
                                )

                                goal, _ = KpiGoal.objects.update_or_create(
                                    id=goal_id,
                                    defaults={
                                        'name': goal_name,
                                        'kpi_category': kpi,
                                        'measurement_type_id': measurement_type_id,
                                        'is_active': True,
                                    }
                                )

                            # Update user-specific goal values
                            # KpiUserGoal.objects.update_or_create(
                            #     kpi_goal=goal,
                            #     kpi_user_year=user_year,
                            #     defaults={
                            #         'goal_value': goal_vals['goal_value'],
                            #         'max_finish_rate': goal_vals['max_finish_rate'],
                            #         'weight': goal_vals['weight'],
                            #         'is_active': True,
                            #         'added_by': request.user,
                            #     }
                            # )
                            user_kpi_goals = KpiUserGoal.objects.select_related('kpi_user_year','kpi_goal').filter(kpi_user_year=user_year,kpi_goal=goal)
                            if user_kpi_goals:
                                user_kpi_goals.update(kpi_goal=goal,goal_value = goal_vals['goal_value'],max_finish_rate = float(goal_vals['max_finish_rate']),weight = float(goal_vals['weight']),is_active=True)
                                edit_flag = 1
                            else:
                                KpiUserGoal.objects.create(kpi_user_year=user_year,kpi_goal=goal,goal_value = goal_vals['goal_value'],max_finish_rate = float(goal_vals['max_finish_rate']),weight = float(goal_vals['weight']),added_by = request.user,is_active=True)
                        
                        # saving to kpi approve table
                        kpi_assignee = KPILevelPermissions.objects.select_related('user_id').filter(user_id_id = user_id,is_active=True,action_type=GOAL)
                        kpi_approval = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpiuser_timeperiod').filter(action_type=GOAL,kpi_user_year__kpi_year_id=period_id,approved_rejected_user_id = user_id)
                        missing_approvals = kpi_assignee.exclude(Exists(kpi_approval.filter(added_by=OuterRef('assigned_to'),kpilevel_id=OuterRef('kpilevel_id'))) )
                        new_approvals = [
                            KPIApprovalStatus(
                                approved_rejected_user_id=user_id,
                                added_by=kpi_usr.assigned_to,
                                kpilevel_id=kpi_usr.kpilevel_id,
                                is_approve=False,
                                is_active=False,
                                kpi_user_year=user_year,
                                action_type=GOAL
                            )
                            for kpi_usr in missing_approvals
                        ]
                        if new_approvals:
                            KPIApprovalStatus.objects.bulk_create(new_approvals)

                        # saving to kpi approve table
                        current_year = KpiYear.objects.get(current_year=True)
                        kpi_approval = KPIApprovalStatus.objects.select_related('approved_rejected_user','kpiuser_timeperiod').filter(action_type=GOAL,kpi_user_year__kpi_year=current_year,approved_rejected_user_id = user_id )
                        if not kpi_approval:
                            kpi_assignee = KPILevelPermissions.objects.select_related('user_id').filter(user_id_id = user_id,is_active=True,action_type=GOAL)
                            for kpi_usr in kpi_assignee:
                                KPIApprovalStatus.objects.create(approved_rejected_user_id = user_id, added_by = kpi_usr.assigned_to, kpilevel_id = kpi_usr.kpilevel_id, is_approve=False, is_active=False,kpi_user_year=user_year[0],action_type=GOAL)
                        else:
                            kpi_approval.update(is_approve=False, is_active=False,approval_status=PENDING)

                        #notification
                        this_user = User.objects.only('first_name','last_name').get(id=user_id)
                        scheme = request.is_secure() and "https://" or "http://"
                        current_site = get_current_site(request)
                        domainlink=scheme+current_site.domain  

                        link = domainlink+'/goal-approve-detail-view/'+encrypt_me(user_id)+'/'+encrypt_me(user_year.kpi_year.id) 
                        # if edit_flag == 1:
                        #     msg = f"{this_user.first_name} {this_user.last_name}'s goal values has been updated. Please restart goal approval"
                        # else:
                        msg = f"{this_user.first_name} {this_user.last_name}'s goal values has been updated. Please start goal approval"

                        assignedto = request.user
                        info = {}
                        info['type'] = 'kpi_goal_permission_kpi'
                        info['action'] = 'Create'
                        # info['action_id'] = int(action)
                        info['url'] = link
                        info['end_user'] = this_user.id
                        notificationUpdate(user_from=request.user,
                                                            user_to=assignedto,
                                                            message=msg, info=info)


                        #email notification
                        mail_subject = 'Update KPI Values'                    
                        user_name = assignedto.first_name+" "+assignedto.last_name
                        content_replace = {"NAME":this_user.first_name+' '+this_user.last_name, "MESSAGE":msg, "LINK":link, "TITLE":"Update KPI values","YEAR": timezone.now().year}
                        send_email_to_users.delay(mail_subject, 4, content_replace, assignedto.email)
                        data['status'] = True
                        data['message'] = "All rows saved successfully."
                        return JsonResponse(data)
        except IntegrityError as e:
            # Handle specific case where measurement_type_id is null
            if 'measurement_type_id' in str(e) and 'not-null constraint' in str(e):
                data['status'] = False
                data['message'] = "Some goals have been deleted and need to be removed from the list before activation. Please remove the deleted goals and try again."
            else:
                data['status'] = False
                data['message'] = "Database integrity error occurred. Please check your data and try again."

            # log entry
            log_data = {}
            log_data['module_name'] = 'assign_kpi_goals'
            log_data['action_type'] = CREATE
            log_data['log_message'] = 'Failed to add KPI and Goal due to integrity error.',
            log_data['status'] = FAILED
            log_data['model_object'] = None
            log_data['db_data'] = {}
            log_data['app_visibility'] = False
            log_data['web_visibility'] = False
            log_data['error_msg'] = traceback.format_exc()
            log_data['fwd_link'] = '/assign-kpi-goal/'
            LogUserActivity(request, log_data)

        except Exception as e:
            import traceback;traceback.print_exc()
            # log entry
            log_data = {}
            log_data['module_name'] = 'assign_kpi_goals'
            log_data['action_type'] = CREATE
            log_data['log_message'] = 'Failed to add KPI and Goal.',
            log_data['status'] = FAILED
            log_data['model_object'] = None
            log_data['db_data'] = {}
            log_data['app_visibility'] = False
            log_data['web_visibility'] = False
            log_data['error_msg'] = traceback.format_exc()
            log_data['fwd_link'] = '/assign-kpi-goal/'
            LogUserActivity(request, log_data)

            data['status'] = False
            data['message'] = "Something went wrong."
        return JsonResponse(data)

class AssignKPIGoalDeleteView(LoginRequiredMixin,View):
    login_url = '/'

    def post(self, request, *args, **kwargs):
        goal_id = request.POST.get('goal_id')
        user_id = request.POST.get('user_id')
        data = {'status': False, 'message': 'Unable to delete the KPI Goal. Please try again.'}

        if goal_id:
            try:
                with transaction.atomic():
                    KpiUserGoal.objects.filter(id=goal_id).delete()
                    KpiUserGoal.objects.filter(kpi_user_year__user=user_id,kpi_user_year__kpi_year__current_year=True).update(is_active=False)
                    data['status'] = True
                    data['message'] = 'The KPI Goal has been successfully removed.'
                    # log entry
                    log_data = {}
                    log_data['module_name'] = 'assign_kpi_goals'
                    log_data['action_type'] = DELETE
                    log_data['log_message'] = f'KPI Goal assigned to the user was successfully removed.',
                    log_data['status'] = SUCCESS
                    log_data['model_object'] = None
                    log_data['db_data'] = {'goal_id': goal_id}
                    log_data['app_visibility'] = True
                    log_data['web_visibility'] = True
                    log_data['error_msg'] = ''
                    log_data['fwd_link'] = '/assign-kpi-goal/'
                    LogUserActivity(request, log_data)

            except Exception as e:
                # log entry
                log_data = {}
                log_data['module_name'] = 'assign_kpi_goals'
                log_data['action_type'] = DELETE
                log_data['log_message'] = f'Failed to delete KPI Goal with ID {goal_id} assigned to the user.',
                log_data['status'] = FAILED
                log_data['model_object'] = None
                log_data['db_data'] = {'goal_id': goal_id}
                log_data['app_visibility'] = False
                log_data['web_visibility'] = False
                log_data['error_msg'] = traceback.format_exc(),
                log_data['fwd_link'] = '/assign-kpi-goal/'
                LogUserActivity(request, log_data)
                data['message'] = f"An error occurred: {str(e)}"
        return JsonResponse(data)

# checking same kpi or goal already exist for autofill
class CheckKpiGoalView(LoginRequiredMixin,View):
    login_url = '/'

    def get(self, request, *args, **kwargs):
        department_id = request.GET.get('department_id')
        kpi_name = request.GET.get('kpi_name', '').strip()
        goal_name = request.GET.get('goal_name', '').strip()
        data = {'status': False, 'message': 'No matching KPI or Goal found.'}
        if department_id:
            kpi = None
            goal = None
            if goal_name:
                goal = KpiGoal.objects.filter(name__iexact=goal_name,kpi_category__department_id=department_id,is_active=True).select_related('measurement_type','kpi_category').first()
                if goal:
                    kpi = goal.kpi_category
            if not goal and kpi_name:
                kpi = KpiCategory.objects.filter(name__iexact=kpi_name,department_id=department_id,is_active=True).select_related('department').first()
                if kpi:
                    goal = KpiGoal.objects.filter(kpi_category=kpi,is_active=True).select_related('kpi_category').first()
            if kpi or goal:
                data['status'] = True
                data['kpi'] = {'id': kpi.id, 'name': kpi.name} if kpi else None
                data['goal'] = {
                    'id': goal.id,
                    'name': goal.name,
                    'kpi_name': goal.kpi_category.name,
                    'measurement_type': {
                        'id': goal.measurement_type.id,
                        'name': goal.measurement_type.name
                    } if goal and goal.measurement_type else None
                } if goal else None
        return JsonResponse(data)
    
class GetTotalWeightView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        user_id = request.GET.get('user_id')
        period_id = request.GET.get('period_id')

        # Calculate total weight from saved records
        total_weight = KpiUserGoal.objects.filter(
            kpi_user_year__user_id=user_id,
            kpi_user_year__kpi_year_id=period_id,kpi_user_year__kpi_year__current_year=True
        ).aggregate(total_weight=Sum('weight'))['total_weight'] or 0

        return JsonResponse({'status': True, 'total_weight': total_weight})

# Get users by department for import export user filter
def get_users_by_department(request):
    user_list = []
    department_id = request.GET.getlist('department_id[]')
    if department_id:
        users = User.objects.filter(is_active=True,profile__department_id__in=department_id).values('id', 'first_name', 'last_name').order_by(Lower("first_name"))
        user_list = [{'id': user['id'], 'name': f"{user['first_name']} {user['last_name']}"} for user in users]
    return JsonResponse({'status': True, 'users': user_list})

class AssignKPIGoalsEditView(LoginRequiredMixin, View):
    login_url = '/'

    def post(self, request, *args, **kwargs):
        data = {}
        try:
            with transaction.atomic():
                goal_id = request.POST.get('goal_id')
                # goal_name = request.POST.get('goal_name', '').strip()
                # kpi_name = request.POST.get('kpi_name', '').strip()
                # measurement_type = request.POST.get('measurement_type')
                goal_value = request.POST.get('goal_value')
                max_finish_rate = request.POST.get('max_finish_rate')
                weight = request.POST.get('weight')
                department_id = request.POST.get('department_id')
                user_id = request.POST.get('user_id')
                period_id = request.POST.get('period_id')
                usergoal_id = request.POST.get('usergoal_id')

                # Get the existing KPI user goal
                kpi_user_goal = KpiUserGoal.objects.select_related('kpi_goal','kpi_goal__kpi_category','kpi_user_year').get(id=usergoal_id)
                # Update only the fields that are provided
                # if kpi_name and kpi_user_goal.kpi_goal.kpi_category.name != kpi_name:
                #     kpi_category = KpiCategory.objects.filter(
                #         name=kpi_name,
                #         department_id=department_id,
                #         is_active=True
                #     ).first()
                #     if not kpi_category:
                #         kpi_category = KpiCategory.objects.create(
                #             name=kpi_name,
                #             department_id=department_id,
                #             is_active=True
                #         )
                #     kpi_user_goal.kpi_goal.kpi_category = kpi_category
                #     kpi_user_goal.kpi_goal.kpi_category.save()

                # if goal_name and kpi_user_goal.kpi_goal.name != goal_name:
                #     kpi_user_goal.kpi_goal.name = goal_name
                #     kpi_user_goal.kpi_goal.save()

                # if measurement_type and kpi_user_goal.kpi_goal.measurement_type_id != measurement_type:
                #     kpi_user_goal.kpi_goal.measurement_type_id = measurement_type
                #     kpi_user_goal.kpi_goal.save()

                if goal_value is not None:
                    kpi_user_goal.goal_value = goal_value
                if max_finish_rate is not None:
                    kpi_user_goal.max_finish_rate = max_finish_rate
                if weight is not None:
                    kpi_user_goal.weight = weight
                kpi_user_goal.is_active = False
                kpi_user_goal.save()

                # Log the update
                log_data = {}
                log_data['module_name'] = 'edit_assign_kpi_goals'
                log_data['action_type'] = UPDATE
                log_data['log_message'] = f'Updated KPI Goal for user ID {user_id}',
                log_data['status'] = SUCCESS
                log_data['model_object'] = kpi_user_goal
                log_data['db_data'] = {
                    'goal_id': goal_id,
                    # 'kpi_name': kpi_name,
                    # 'goal_name': goal_name,
                    # 'measurement_type': measurement_type,
                    'goal_value': goal_value,
                    'max_finish_rate': max_finish_rate,
                    'weight': weight
                }
                log_data['app_visibility'] = True
                log_data['web_visibility'] = True
                log_data['error_msg'] = ''
                log_data['fwd_link'] = '/assign-kpi-goal/'
                LogUserActivity(request, log_data)

                context = {
                    'userid': user_id,
                    'period': period_id,
                    'selected_user': User.objects.get(id=user_id),
                    'measurement_types': MeasurementType.objects.filter(is_active=True),
                    'categories': KpiCategory.objects.filter(
                        # is_active=True,
                        department_id=department_id
                    ).prefetch_related(
                        Prefetch(
                            'kpi_goals',
                            queryset=KpiGoal.objects.all().annotate(
                                has_user_goal=Exists(
                                    KpiUserGoal.objects.filter(kpi_goal=OuterRef('id'),kpi_user_year=kpi_user_goal.kpi_user_year)
                                )
                            ).filter(has_user_goal=True).select_related('measurement_type').annotate(
                                user_goal_id=F('kpiusergoal__id'),
                                goal_value=F('kpiusergoal__goal_value'),
                                max_finish_rate=F('kpiusergoal__max_finish_rate'),
                                weight=F('kpiusergoal__weight')
                            ).order_by('kpi_category_id', '-id').distinct('kpi_category_id')
                        )
                    ).order_by("department", "id")
                }
                KpiUserGoal.objects.select_related('kpi_user_year','kpi_user_year__user','kpi_user_year__kpi_year').filter(kpi_user_year__user_id=user_id,kpi_user_year__kpi_year__current_year=True).update(is_active=False)

                data['status'] = True
                data['message'] = "KPI Goal updated successfully"
                data['template'] = loader.render_to_string(
                    'hisensehr/kpi/goalset/assign_kpi_goal_values_ajax.html',
                    context,
                    request=request
                )

        except Exception as e:
            log_data = {}
            log_data['module_name'] = 'edit_assign_kpi_goals'
            log_data['action_type'] = UPDATE
            log_data['log_message'] = 'Failed to update KPI Goal',
            log_data['status'] = FAILED
            log_data['model_object'] = None
            log_data['db_data'] = {}
            log_data['app_visibility'] = False
            log_data['web_visibility'] = False
            log_data['error_msg'] = traceback.format_exc(),
            log_data['fwd_link'] = '/assign-kpi-goal/'
            LogUserActivity(request, log_data)
            data['status'] = False
            data['message'] = str(e)

        return JsonResponse(data)
    

class KPIPerformancePdf(SuperuserOrPermissionRequiredMixin, View):
    permission_required = 'hisensehr.view_kpilevels'

    def get(self, request, *args, **kwargs):
        try:
            data = {}
            quater_id = request.GET.get('quater_id')
            department_id = request.GET.get('department_id')
            designation_id = request.GET.get('designation_id')
            year_id = request.GET.get('year_id')
            status = request.GET.get('status', 'approved')
            search = request.GET.get('search', '')

            # Get the quarter details
            quater = KpiTimePeriod.objects.select_related('kpi_year').get(id=quater_id)
            data['quater'] = quater
            # Get distinct users with their KPI performance data
            distinct_users_list = KpiUserTimePeriod.objects.select_related(
                'kpi_time_period',
                'kpi_user_year__user',
                'kpi_user_year__user__profile',
                'kpi_user_year__user__profile__department',
                'kpi_user_year__user__profile__designation',
                'approved_by',
            ).filter(
                kpi_time_period_id=quater_id,
                kpi_approval_status=status
            )

            if department_id:
                distinct_users_list = distinct_users_list.filter(
                    kpi_user_year__user__profile__department_id=department_id
                )
            if designation_id:
                distinct_users_list = distinct_users_list.filter(
                    kpi_user_year__user__profile__designation_id=designation_id
                )
            if year_id:
                distinct_users_list = distinct_users_list.filter(
                    kpi_user_year__kpi_year_id=year_id
                )
            if search:
                distinct_users_list = distinct_users_list.filter(
                    Q(kpi_user_year__user__first_name__icontains=search) |
                    Q(kpi_user_year__user__last_name__icontains=search) |
                    Q(kpi_user_year__user__profile__employee_id__icontains=search)
                )

            
            distinct_users_list = sorted(
                distinct_users_list,
                key=lambda x: (
                    x.kpi_user_year.user.first_name.lower() if x.kpi_user_year and x.kpi_user_year.user and x.kpi_user_year.user.first_name else '',
                    x.kpi_user_year.user.last_name.lower() if x.kpi_user_year and x.kpi_user_year.user and x.kpi_user_year.user.last_name else ''
                )
            )

            # Add index/sl.no to each user in the list
            indexed_users_list = []
            for idx, user in enumerate(distinct_users_list, start=1):
                indexed_users_list.append({'sl_no': idx, 'user_obj': user})

            data['indexed_users_list'] = indexed_users_list
            template = loader.get_template('hisensehr/pdf/kpi_performance_pdf.html')
            html_string = template.render(data)
            pdf_file = HTML(string=html_string, base_url=request.build_absolute_uri()).write_pdf()
            
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = f'filename="KPI_Performance_{quater.kpi_year.name}_{quater.name}.pdf"'
            response['Content-Transfer-Encoding'] = 'utf-8'
            return response

        except Exception as e:
            log_data = {
                'module_name': 'kpi_performance_pdf',
                'action_type': READ,
                'log_message': '',
                'status': FAILED,
                'model_object': None,
                'db_data': {},
                'app_visibility': False,
                'web_visibility': False,
                'error_msg': traceback.format_exc(),
                'fwd_link': '/kpi-performance/'
            }
            LogUserActivity(request, log_data)
            data['status'] = False
            data['message'] = str(e)
        return JsonResponse(data)
